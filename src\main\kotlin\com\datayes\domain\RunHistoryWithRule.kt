package com.datayes.domain

import java.time.LocalDateTime

/**
 * DTO for displaying detailed run history information, merging IssueRunHistoryEntity and TechRuleEntity.
 */
data class RunHistoryWithRule(
    // Fields from IssueRunHistoryEntity
    val id: Long?,
    val issueId: Long,
    val taskId: Long,
    val ruleId: Long,
    val runId: String,
    val startTime: LocalDateTime,
    val endTime: LocalDateTime?,
    val totalScanCount: Long?,
    val errorRowCount: Int?,
    val status: RunStatus,
    val errorMessage: String?,

    // Fields from TechRuleEntity
    val ruleName: String,
    val systemName: Long?,
    val ruleCategory: Long?,
    val ruleType: Long?,
    val directorBy: String?,
    val ruleStatus: Byte?,
    val ruleDescription: String?,
    val checkType: Byte?,
    val dataSource: String?,
    val databaseName: String?,
    val tableName: String?,
    val ruleCreateBy: String?,
    val ruleCreateTime: LocalDateTime?,
    val ruleUpdateBy: String?,
    val ruleUpdateTime: LocalDateTime?
)

/**
 * Helper function to merge IssueRunHistoryEntity and TechRuleEntity into RunHistoryWithRule
 */
fun mergeRunHistoryAndRule(
    runHistory: IssueRunHistoryEntity,
    techRule: TechRuleEntity
): RunHistoryWithRule {
    return RunHistoryWithRule(
        // Mapping fields from IssueRunHistoryEntity
        id = runHistory.id,
        issueId = runHistory.issueId,
        taskId = runHistory.taskId,
        ruleId = runHistory.ruleId,
        runId = runHistory.runId,
        startTime = runHistory.startTime,
        endTime = runHistory.endTime,
        totalScanCount = runHistory.totalScanCount,
        errorRowCount = runHistory.errorRowCount,
        status = runHistory.status,
        errorMessage = runHistory.errorMessage,

        // Mapping fields from TechRuleEntity
        ruleName = techRule.ruleName,
        systemName = techRule.systemName,
        ruleCategory = techRule.ruleCategory,
        ruleType = techRule.ruleType,
        directorBy = techRule.directorBy,
        ruleStatus = techRule.ruleStatus,
        ruleDescription = techRule.ruleDescription,
        checkType = techRule.checkType,
        dataSource = techRule.dataSource,
        databaseName = techRule.databaseName,
        tableName = techRule.tableName,
        ruleCreateBy = techRule.createBy,
        ruleCreateTime = techRule.createTime,
        ruleUpdateBy = techRule.updateBy,
        ruleUpdateTime = techRule.updateTime
    )
}