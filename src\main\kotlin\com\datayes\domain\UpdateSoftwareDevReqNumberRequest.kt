package com.datayes.domain

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "更新软开需求编号请求")
data class UpdateSoftwareDevReqNumberRequest(
    @Schema(description = "规则执行结果ID列表，如果为空则使用ruleId", example = "[1, 2, 3]")
    val ids: List<Long> = emptyList(),
    
    @Schema(description = "规则ID，当ids为空时使用此字段更新该规则下的所有记录", example = "123")
    val ruleId: Long? = null,
    
    @Schema(description = "新增软开需求编号", required = true, example = "REQ-2023-001")
    val newSoftwareDevReqNumber: String,
    
    @Schema(description = "操作类型", required = true, example = "INSERT", allowableValues = ["INSERT", "UPDATE"])
    val operationType: String = "INSERT",
    
    @Schema(description = "更新人", example = "admin")
    val updatedBy: String? = null
)