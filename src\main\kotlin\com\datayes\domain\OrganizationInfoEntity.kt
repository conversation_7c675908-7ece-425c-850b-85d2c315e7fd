package com.datayes.domain

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime

@Table("organization_info")
data class OrganizationInfoEntity(
    @Id
    val id: Long? = null,

    @Column("org_name")
    val orgName: String,

    @Column("CREATE_BY")
    val createBy: String? = null,

    @Column("CREATE_TIME")
    val createTime: LocalDateTime? = null,

    @Column("UPDATE_BY")
    val updateBy: String? = null,

    @Column("UPDATE_TIME")
    val updateTime: LocalDateTime? = null,
)