package com.datayes.domain

import org.slf4j.MDC

/**
 * 标准API响应数据结构
 * @param T 负载数据类型
 * @property success 是否成功
 * @property message 响应消息
 * @property payload 响应数据负载
 * @property requestId 请求ID，用于日志追踪
 */
data class StandardResponse<T>(
    val success: Boolean,
    val message: String,
    val payload: T?,
    val requestId: String? = MDC.get("requestId"),
) {
    companion object {
        /**
         * 创建成功响应
         */
        fun <T> success(message: String = "操作成功", payload: T? = null): StandardResponse<T> {
            return StandardResponse(true, message, payload, MDC.get("requestId"))
        }

        /**
         * 创建失败响应
         */
        fun <T> failure(message: String, payload: T? = null): StandardResponse<T> {
            return StandardResponse(false, message, payload, MDC.get("requestId"))
        }
    }
}