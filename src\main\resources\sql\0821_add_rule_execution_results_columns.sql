-- Migration: Add new columns to rule_execution_results table
-- Date: 2025-08-21
-- Description: Add software dev requirement number, issue cause type, and issue cause detail columns

-- Forward migration
ALTER TABLE rule_execution_results 
ADD COLUMN SOFTWARE_DEV_REQ_NUMBER_LIST JSON NULL COMMENT '软开需求编号列表' AFTER IS_FIXED,
ADD COLUMN ASSIGNMENT_STATUS varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '未分配' COMMENT '分配状态: 已分配/未分配' AFTER SOFTWARE_DEV_REQ_NUMBER_LIST,
ADD COLUMN ISSUE_CAUSE_TYPE varchar(255) COLLATE utf8mb4_unicode_ci NULL COMMENT '问题原因类型' AFTER ASSIGNMENT_STATUS,
ADD COLUMN ISSUE_CAUSE_DETAIL text COLLATE utf8mb4_unicode_ci NULL COMMENT '问题原因详述' AFTER ISSUE_CAUSE_TYPE;

-- Add indexes for performance
CREATE INDEX idx_assignment_status ON rule_execution_results(ASSIGNMENT_STATUS);
CREATE INDEX idx_issue_cause_type ON rule_execution_results(ISSUE_CAUSE_TYPE);

-- Rollback SQL (to be executed if rollback is needed)
/*
-- Drop indexes
DROP INDEX idx_issue_cause_type ON rule_execution_results;
DROP INDEX idx_assignment_status ON rule_execution_results;

-- Drop columns
ALTER TABLE rule_execution_results 
DROP COLUMN ISSUE_CAUSE_DETAIL,
DROP COLUMN ISSUE_CAUSE_TYPE,
DROP COLUMN ASSIGNMENT_STATUS,
DROP COLUMN SOFTWARE_DEV_REQ_NUMBER_LIST;
*/