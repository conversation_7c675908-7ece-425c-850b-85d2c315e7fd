# Implementation Plan

- [ ] 1. Analyze and document current query differences
  - Compare the SQL logic between `searchIssuesGroupedByRuleId` and `searchIssuesCountGroupByRule` methods
  - Identify specific differences in WHERE conditions, subqueries, and JOIN logic
  - Document the exact cause of the pagination mismatch
  - _Requirements: 1.1, 2.2_

- [ ] 2. Create comprehensive test cases for pagination scenarios
  - Write unit tests that reproduce the pagination bug with `isFixed` parameter
  - Create test cases for rules with multiple task associations
  - Add tests to verify count matches content across different page sizes
  - _Requirements: 1.1, 1.2, 1.3_

- [ ] 3. Fix the count query for general search (without taskId)
  - Update `searchIssuesCountGroupByRule` SQL query to match the deduplication logic of `searchIssuesGroupedByRuleId`
  - Ensure identical WHERE conditions and subquery structure for rule deduplication
  - Verify the `isFixed` filter is applied consistently after deduplication
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 4. Fix the count query for task-specific search (with taskId)
  - Update `searchIssuesCountGroupByRuleWithTaskId` SQL query to match `searchIssuesGroupedByRuleIdWithTaskId`
  - Align the subquery logic for selecting the most recent issue per rule within the specified task
  - Ensure consistent filter application order
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 5. Add integration tests with database queries
  - Create tests that execute actual database queries with test data
  - Verify that count queries return the same number of unique rules as content queries
  - Test various combinations of filters (isFixed, taskId, etc.) to ensure consistency
  - _Requirements: 1.1, 1.2, 1.3_

- [ ] 6. Validate backward compatibility and API contract
  - Run existing tests to ensure no breaking changes to API response structure
  - Verify that all existing query parameters continue to work as expected
  - Test default behavior when no pagination parameters are provided
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 7. Performance testing and optimization
  - Execute performance tests to ensure the query fixes don't degrade response times
  - Verify that database indexes are being used effectively
  - Add any necessary database hints or optimizations if performance issues are detected
  - _Requirements: 2.1, 2.2_