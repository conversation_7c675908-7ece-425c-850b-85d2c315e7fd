package com.datayes.spring

import com.datayes.domain.RuleExecutionLogEntity
import org.springframework.data.repository.CrudRepository
import org.springframework.stereotype.Repository

@Repository
interface RuleExecutionLogRepository : CrudRepository<RuleExecutionLogEntity, Long> {

    /**
     * 根据规则ID和运行ID查找规则执行日志
     */
    fun findByRuleIdAndRunId(ruleId: Long, runId: String): List<RuleExecutionLogEntity>

    /**
     * 查找指定规则ID的最新一条执行日志，用于获取最新的运行ID
     */
    fun findTopByRuleIdOrderByTimestampDesc(ruleId: Long): RuleExecutionLogEntity?

    /**
     * 根据规则ID查找所有规则执行日志
     */
    fun findAllByRuleId(ruleId: Long): List<RuleExecutionLogEntity>
}
