package com.datayes.spring

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest

@SpringBootTest
class RuleResultRepositoryTest {

    @Autowired
    lateinit var repository: RuleResultRepository

    @Test
    fun `select by id 1 should success`() {
        repository.findById(1L)
    }

    @Test
    fun `findAllByRuleId should return results for existing rule ID`() {
        // Use existing rule ID from database - 10000204 has confirmed data
        val ruleId = 10000204L
        
        // Execute the method under test
        val results = repository.findAllByRuleId(ruleId)
        
        // Verify results are returned (read-only operation)
        assertThat(results).isNotEmpty()
        assertThat(results).allMatch { it.ruleId == ruleId }
        
        // Verify basic properties are populated correctly
        results.forEach { result ->
            assertThat(result.id).isNotNull()
            assertThat(result.ruleId).isEqualTo(ruleId)
            assertThat(result.dbType).isNotNull()
            assertThat(result.originalSql).isNotNull()
            assertThat(result.runId).isNotNull()
            assertThat(result.executedAt).isNotNull()
            assertThat(result.rowData).isNotNull()
            assertThat(result.rowIndex).isNotNull()
            assertThat(result.executionTimeMs).isNotNull()
            assertThat(result.createdAt).isNotNull()
        }
    }

    @Test
    fun `findAllByRuleId should return empty list for non-existent rule ID`() {
        // Use a rule ID that doesn't exist in the database
        val nonExistentRuleId = 99999999L
        
        // Execute the method under test
        val results = repository.findAllByRuleId(nonExistentRuleId)
        
        // Verify empty results for non-existent rule ID
        assertThat(results).isEmpty()
    }
}