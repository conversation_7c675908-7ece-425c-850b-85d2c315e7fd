package com.datayes

import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import javax.sql.DataSource
import org.junit.jupiter.api.Assertions.*

@SpringBootTest
class DatabaseConfigIntegrationTests {

    @Autowired
    private lateinit var dataSource: DataSource

    @Test
    fun `dataSource should be configured`() {
        assertNotNull(dataSource)
    }

    @Test
    fun `database connection should be successful`() {
        dataSource.connection.use { connection ->
            assertFalse(connection.isClosed)
            assertEquals("dgp", connection.catalog)
        }
    }
}