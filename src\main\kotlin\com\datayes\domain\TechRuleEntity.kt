package com.datayes.domain

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table

import java.time.LocalDateTime

@Table("tech_rule")
data class TechRuleEntity(

    @Id
    val id: Long? = null,

    @Column("RULE_NAME")
    val ruleName: String,

    @Column("SYSTEM_NAME")
    val systemName: Long? = null,

    @Column("RULE_CATEGORY")
    val ruleCategory: Long? = null, // 领域id

    @Column("RULE_TYPE")
    val ruleType: Long? = null,

    @Column("DIRECTOR_BY")
    val directorBy: String? = null,

    @Column("RULE_STATUS")
    val ruleStatus: Byte? = null,

    @Column("CREATE_BY")
    val createBy: String? = null,

    @Column("CREATE_TIME")
    val createTime: LocalDateTime? = null,

    @Column("UPDATE_BY")
    val updateBy: String? = null,

    @Column("UPDATE_TIME")
    val updateTime: LocalDateTime? = null,

    @Column("QA_ACTIVE_FLG")
    val qaActiveFlg: Boolean = true,

    @Column("IMPORTANCE_LEVEL")
    val importanceLevel: String? = null,

    @Column("BUSINESS_RULE_ID")
    val businessRuleId: Long? = null,

    @Column("RULE_DESCRIPTION")
    val ruleDescription: String? = null,

    @Column("CHECK_TYPE")
    val checkType: Byte? = null,

    @Column("DATA_SOURCE")
    val dataSource: String? = null,

    @Column("DATABASE_NAME")
    val databaseName: String? = null,

    @Column("TABLE_NAME")
    val tableName: String? = null,

    @Column("TABLE_ALIAS")
    val tableAlias: String? = null,

    @Column("RULE_SQL")
    val ruleSql: String? = null,

    @Column("RELATED_CONFIG")
    val relatedConfig: String? = null,
)
