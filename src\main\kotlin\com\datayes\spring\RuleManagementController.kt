package com.datayes.spring

import com.datayes.domain.DeletionResult
import com.datayes.domain.StandardResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/rule-management")
@Tag(name = "规则管理", description = "跨表规则管理操作")
class RuleManagementController(
    private val issueRepository: IssueRepository,
    private val issueRunHistoryRepository: IssueRunHistoryRepository,
    private val ruleExecutionLogRepository: RuleExecutionLogRepository,
    private val ruleResultRepository: RuleResultRepository
) {

    private val log = LoggerFactory.getLogger(RuleManagementController::class.java)

    @PostMapping("/delete-execution-by-rule/{ruleId}")
    @Operation(
        summary = "根据规则ID删除相关数据",
        description = "删除与指定规则ID相关的所有数据，包括问题记录、运行历史、执行日志和执行结果"
    )
    @ApiResponse(responseCode = "200", description = "成功删除所有相关数据")
    fun deleteByRuleId(
        @Parameter(description = "规则ID", required = true)
        @PathVariable ruleId: Long
    ): StandardResponse<DeletionResult> {
        log.info("4fbe1e46 | deleteByRuleId | 开始 | 根据规则ID删除相关数据 | ruleId: {}", ruleId)
        val startTime = System.currentTimeMillis()

        try {
            // 查找并删除规则结果
            val ruleResults = ruleResultRepository.findAllByRuleId(ruleId)
            val ruleResultCount = ruleResults.size
            log.info("4fbe1e46 | deleteByRuleId | 查找到规则结果 | ruleId: {} | count: {}", ruleId, ruleResultCount)
            ruleResults.forEach { ruleResultRepository.delete(it) }
            log.info("4fbe1e46 | deleteByRuleId | 删除规则结果完成 | ruleId: {} | count: {}", ruleId, ruleResultCount)

            // 查找并删除规则执行日志
            val executionLogs = ruleExecutionLogRepository.findAllByRuleId(ruleId)
            val executionLogCount = executionLogs.size
            log.info("4fbe1e46 | deleteByRuleId | 查找到规则执行日志 | ruleId: {} | count: {}", ruleId, executionLogCount)
            executionLogs.forEach { ruleExecutionLogRepository.delete(it) }
            log.info("4fbe1e46 | deleteByRuleId | 删除规则执行日志完成 | ruleId: {} | count: {}", ruleId, executionLogCount)

            // 查找并删除问题运行历史
            val runHistories = issueRunHistoryRepository.findAllByRuleId(ruleId)
            val runHistoryCount = runHistories.size
            log.info("4fbe1e46 | deleteByRuleId | 查找到问题运行历史 | ruleId: {} | count: {}", ruleId, runHistoryCount)
            runHistories.forEach { issueRunHistoryRepository.delete(it) }
            log.info("4fbe1e46 | deleteByRuleId | 删除问题运行历史完成 | ruleId: {} | count: {}", ruleId, runHistoryCount)

            // 查找并删除问题记录
            log.info("4fbe1e46 | deleteByRuleId | 开始查找问题记录 | ruleId: {}", ruleId)
            val issues = issueRepository.findByRuleId(ruleId)
            val issueCount = issues.size
            log.info("4fbe1e46 | deleteByRuleId | 查找到问题记录 | ruleId: {} | count: {}", ruleId, issueCount)
            issues.forEach { issueRepository.delete(it) }
            log.info("4fbe1e46 | deleteByRuleId | 删除问题记录完成 | ruleId: {} | count: {}", ruleId, issueCount)

            // 返回删除的记录数
            val deletionResult = DeletionResult(
                ruleResults = ruleResultCount,
                executionLogs = executionLogCount,
                runHistories = runHistoryCount,
                issues = issueCount
            )

            val elapsedTime = System.currentTimeMillis() - startTime
            log.info("4fbe1e46 | deleteByRuleId | 完成 | 根据规则ID删除相关数据 | ruleId: {} | 总删除记录数: {} | 耗时: {}ms", 
                ruleId, ruleResultCount + executionLogCount + runHistoryCount + issueCount, elapsedTime)

            return StandardResponse.success(
                message = "成功删除规则ID $ruleId 的相关数据",
                payload = deletionResult
            )
        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error("4fbe1e46 | deleteByRuleId | 异常 | 根据规则ID删除相关数据失败 | ruleId: {} | 耗时: {}ms | 异常: {}", 
                ruleId, elapsedTime, e.message, e)
            throw e
        }
    }
}
