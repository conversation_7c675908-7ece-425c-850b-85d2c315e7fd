package com.datayes.domain

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table

import java.time.LocalDateTime

@Table("validate_task")
data class ValidateTaskEntity(

    @Id
    val id: Long? = null,

    @Column("TASK_NAME")
    val taskName: String,

    @Column("TASK_DESC")
    val taskDesc: String? = null,

    @Column("SYSTEM_NAME")
    val systemName: String? = null,

    @Column("TASK_TYPE")
    val taskType: String? = null,

    @Column("CRON_EXPRESSION")
    val cronExpression: String? = null,

    @Column("LAST_EXECUTE_RESULT")
    val lastExecuteResult: String? = null,

    @Column("LAST_START_TIME")
    val lastStartTime: LocalDateTime? = null,

    @Column("LAST_END_TIME")
    val lastEndTime: LocalDateTime? = null,

    @Column("NEXT_RUN_TIME")
    val nextRunTime: LocalDateTime? = null,

    @Column("TASK_STATUS")
    val taskStatus: String? = null,

    @Column("OWNER")
    val owner: String? = null,

    @Column("PRE_TASK_IDS")
    val preTaskIds: String? = null,

    @Column("EMAIL_ADDRESSES")
    val emailAddresses: String? = null,

    @Column("QA_ACTIVE_FLG")
    val qaActiveFlg: Boolean = true,

    @Column("CREATE_BY")
    val createBy: String? = null,

    @Column("CREATE_TIME")
    val createTime: LocalDateTime? = null,

    @Column("UPDATE_BY")
    val updateBy: String? = null,

    @Column("UPDATE_TIME")
    val updateTime: LocalDateTime? = null,

    @Column("COMPENSATION_STRATEGY")
    val compensationStrategy: String? = null,

    @Column("SCHEDULE_START_BY")
    val scheduleStartBy: String? = null,

    @Column("SCHEDULE_START_TIME")
    val scheduleStartTime: LocalDateTime? = null,

    @Column("SCHEDULE_STOP_BY")
    val scheduleStopBy: String? = null,

    @Column("SCHEDULE_STOP_TIME")
    val scheduleStopTime: LocalDateTime? = null,
)
