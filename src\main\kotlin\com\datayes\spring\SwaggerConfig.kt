package com.datayes.spring

import io.swagger.v3.oas.annotations.OpenAPIDefinition
import io.swagger.v3.oas.annotations.info.Info
import org.springframework.context.annotation.Configuration

@Configuration
@OpenAPIDefinition(
    info = Info(
        title = "Data Quality Issue Management API",
        version = "1.0",
        description = "API documentation for Data Quality Issue Management System"
    )
)
class SwaggerConfig