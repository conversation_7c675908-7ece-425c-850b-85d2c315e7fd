package com.datayes.domain

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "更新规则执行结果属性请求")
data class UpdateRuleResultPropertiesRequest(
    @Schema(description = "规则执行结果ID", required = true, example = "1")
    val id: Long,
    
    @Schema(description = "新增软开需求编号 (将添加到列表末尾)", example = "REQ-2023-001")
    val newSoftwareDevReqNumber: String? = null,
    
    @Schema(description = "问题原因类型", example = "数据质量")
    val issueCauseType: String? = null,
    
    @Schema(description = "问题原因详述", example = "字段值不符合规范要求")
    val issueCauseDetail: String? = null,
    
    @Schema(description = "更新人", example = "admin")
    val updatedBy: String? = null
)