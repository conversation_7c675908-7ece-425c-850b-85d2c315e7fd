package com.datayes.spring

import com.datayes.domain.IssueEntity
import com.datayes.domain.IssueStage
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.dao.OptimisticLockingFailureException
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@SpringBootTest
class IssueRepositoryTest {

    @Autowired
    private lateinit var issueRepository: IssueRepository

    @Test
    fun `should find issue by id 1 without exception`() {
        issueRepository.findById(1L)
    }

    @Test
    @Transactional
    fun `should increment version on update and detect optimistic locking conflict`() {
        val saved = issueRepository.save(
            IssueEntity(
                id = null,
                ruleId = 123L,
                taskId = 456L,
                assignedTo = "user1",
                currentStage = IssueStage.NOT_STARTED,
                createBy = "tester",
                createdAt = LocalDateTime.now(),
                updateBy = "tester",
                updatedAt = LocalDateTime.now()
            )
        )
        val id = saved.id ?: error("ID should not be null")

        // 模拟两个不同的会话加载同一条记录
        val entity1 = issueRepository.findById(id).get()
        val entity2 = issueRepository.findById(id).get()

        // 修改第一个实体并保存，版本号应该+1
        val updated1 = entity1.copy(currentStage = IssueStage.RUNNING)
        val saved1 = issueRepository.save(updated1)

        // 断言版本号递增
        assertThat(saved1.version == entity1.version + 1)

        // 修改第二个实体并尝试保存，应该抛出乐观锁异常
        val updated2 = entity2.copy(currentStage = IssueStage.COMPLETED)
        assertThrows(OptimisticLockingFailureException::class.java) {
            issueRepository.save(updated2)
        }
    }

    @Test
    fun `searchIssuesCountGroupByRule should return count greater than zero when all filters are null`() {
        val count = issueRepository.searchIssuesCountGroupByRule(
            taskName = null,
            systemList = null,
            ruleName = null,
            assignedToList = null,
            businessRuleName = null,
            orgName = null,
            isFixed = null
        )
        assertThat(count).isGreaterThan(0L)
    }
}
