package com.datayes.spring

import com.datayes.domain.IssueEntity
import org.springframework.data.jdbc.repository.query.Query
import org.springframework.data.repository.CrudRepository
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface IssueRepository : CrudRepository<IssueEntity, Long> {

    // find by rule id
    fun findByRuleId(ruleId: Long): List<IssueEntity>

    /**
     * 根据条件搜索数据质量问题
     *
     * @param taskId 校验任务ID
     * @param taskName 校验任务名称（模糊搜索）
     * @param systemList 所属系统（多选）
     * @param ruleName 技术规则名称（模糊搜索）
     * @param businessRuleName 业务规则名称（模糊搜索）
     * @param orgName 组织名称（模糊搜索）
     * @param assignedTo 负责人（多选）
     * @param isFixed 是否已修复
     * @param sortField 排序字段
     * @param sortOrder 排序方向
     * @param limit 分页大小
     * @param offset 分页偏移量
     * @return 分页的数据质量问题列表
     */
    @Query(
        """
        SELECT distinct i.* FROM data_quality_issues i
        JOIN tech_rule r ON i.RULE_ID = r.ID
        LEFT JOIN business_rule_info bri ON r.BUSINESS_RULE_ID = bri.id
        LEFT JOIN organization_info oi ON bri.ORG_CODE = oi.id
        JOIN validate_task_rule vtr ON r.ID = vtr.RULE_ID
        JOIN validate_task vt ON vtr.TASK_ID = vt.ID
        WHERE (:taskId IS NULL OR vt.ID = :taskId)
        AND (:taskName IS NULL OR vt.TASK_NAME LIKE CONCAT('%', :taskName, '%'))
        AND (:ruleName IS NULL OR r.RULE_NAME LIKE CONCAT('%', :ruleName, '%'))
        AND (:businessRuleName IS NULL OR bri.RULE_NAME LIKE CONCAT('%', :businessRuleName, '%'))
        AND (:orgName IS NULL OR oi.org_name LIKE CONCAT('%', :orgName, '%'))
        AND (:systemList IS NULL OR r.SYSTEM_NAME IN (:systemList))
        AND (:assignedToList IS NULL OR i.ASSIGNED_TO IN (:assignedToList))
        AND (
            :isFixed IS NULL OR 
            (:isFixed = true AND i.CURRENT_STAGE = 'COMPLETED' AND COALESCE(i.LATEST_ERROR_ROW_COUNT, 0) = 0) OR
            (:isFixed = false AND NOT (i.CURRENT_STAGE = 'COMPLETED' AND COALESCE(i.LATEST_ERROR_ROW_COUNT, 0) = 0))
        )
        ORDER BY 
            CASE WHEN :sortField = 'remainingCount' AND :sortOrder = 'ASC' THEN i.LATEST_ERROR_ROW_COUNT END ASC,
            CASE WHEN :sortField = 'remainingCount' AND :sortOrder = 'DESC' THEN i.LATEST_ERROR_ROW_COUNT END DESC,
            i.CREATE_AT DESC
        LIMIT :limit OFFSET :offset
    """
    )
    fun searchIssues(
        @Param("taskId") taskId: Long?,
        @Param("taskName") taskName: String?,
        @Param("systemList") systemList: List<String>?,
        @Param("ruleName") ruleName: String?,
        @Param("businessRuleName") businessRuleName: String?,
        @Param("orgName") orgName: String?,
        @Param("assignedToList") assignedToList: List<String>?,
        @Param("isFixed") isFixed: Boolean?,
        @Param("sortField") sortField: String? = null,
        @Param("sortOrder") sortOrder: String? = null,
        @Param("limit") limit: Int,
        @Param("offset") offset: Int,
    ): List<IssueEntity>

    /**
     * 根据条件搜索数据质量问题，按 ruleId 分组并选择最新的 updatedAt
     *
     * @param taskName 校验任务名称（模糊搜索）
     * @param systemList 所属系统（多选）
     * @param ruleName 技术规则名称（模糊搜索）
     * @param businessRuleName 业务规则名称（模糊搜索）
     * @param orgName 组织名称（模糊搜索）
     * @param assignedToList 负责人（多选）
     * @param isFixed 是否已修复
     * @param sortField 排序字段
     * @param sortOrder 排序方向
     * @param limit 分页大小
     * @param offset 分页偏移量
     * @return 分页的数据质量问题列表
     */
    @Query(
        """
        SELECT distinct i.* FROM data_quality_issues i
        JOIN tech_rule r ON i.RULE_ID = r.ID
        LEFT JOIN business_rule_info bri ON r.BUSINESS_RULE_ID = bri.id
        LEFT JOIN organization_info oi ON bri.ORG_CODE = oi.id
        JOIN validate_task_rule vtr ON r.ID = vtr.RULE_ID
        JOIN validate_task vt ON vtr.TASK_ID = vt.ID
        WHERE (:taskName IS NULL OR vt.TASK_NAME LIKE CONCAT('%', :taskName, '%'))
        AND (:ruleName IS NULL OR r.RULE_NAME LIKE CONCAT('%', :ruleName, '%'))
        AND (:businessRuleName IS NULL OR bri.RULE_NAME LIKE CONCAT('%', :businessRuleName, '%'))
        AND (:orgName IS NULL OR oi.org_name LIKE CONCAT('%', :orgName, '%'))
        AND (:systemList IS NULL OR r.SYSTEM_NAME IN (:systemList))
        AND (:assignedToList IS NULL OR i.ASSIGNED_TO IN (:assignedToList))
        AND (
            :isFixed IS NULL OR 
            (:isFixed = true AND i.CURRENT_STAGE = 'COMPLETED' AND COALESCE(i.LATEST_ERROR_ROW_COUNT, 0) = 0) OR
            (:isFixed = false AND NOT (i.CURRENT_STAGE = 'COMPLETED' AND COALESCE(i.LATEST_ERROR_ROW_COUNT, 0) = 0))
        )
        AND i.UPDATE_AT = (
            SELECT MAX(UPDATE_AT)
            FROM data_quality_issues sub
            WHERE sub.RULE_ID = i.RULE_ID
        )
        ORDER BY 
            CASE WHEN :sortField = 'remainingCount' AND :sortOrder = 'ASC' THEN i.LATEST_ERROR_ROW_COUNT END ASC,
            CASE WHEN :sortField = 'remainingCount' AND :sortOrder = 'DESC' THEN i.LATEST_ERROR_ROW_COUNT END DESC,
            i.UPDATE_AT DESC
        LIMIT :limit OFFSET :offset
    """
    )
    fun searchIssuesGroupedByRuleId(
        @Param("taskName") taskName: String?,
        @Param("systemList") systemList: List<String>?,
        @Param("ruleName") ruleName: String?,
        @Param("businessRuleName") businessRuleName: String?,
        @Param("orgName") orgName: String?,
        @Param("assignedToList") assignedToList: List<String>?,
        @Param("isFixed") isFixed: Boolean?,
        @Param("sortField") sortField: String?,
        @Param("sortOrder") sortOrder: String?,
        @Param("limit") limit: Int,
        @Param("offset") offset: Int,
    ): List<IssueEntity>

    /**
     * 根据条件搜索数据质量问题的总记录数
     *
     * @param taskId 校验任务ID
     * @param taskName 校验任务名称（模糊搜索）
     * @param systemList 所属系统（多选）
     * @param ruleName 技术规则名称（模糊搜索）
     * @param businessRuleName 业务规则名称（模糊搜索）
     * @param orgName 组织名称（模糊搜索）
     * @param assignedTo 负责人（多选）
     * @param status 问题处理状态（多选）
     * @return 符合条件的总记录数
     */
    @Query(
        """
        SELECT COUNT(*) FROM data_quality_issues i
        JOIN tech_rule r ON i.RULE_ID = r.ID
        LEFT JOIN business_rule_info bri ON r.BUSINESS_RULE_ID = bri.id
        LEFT JOIN organization_info oi ON bri.ORG_CODE = oi.id
        JOIN validate_task_rule vtr ON r.ID = vtr.RULE_ID
        JOIN validate_task vt ON vtr.TASK_ID = vt.ID
        WHERE (:taskId IS NULL OR vt.ID = :taskId)
        AND (:taskName IS NULL OR vt.TASK_NAME LIKE CONCAT('%', :taskName, '%'))
        AND (:ruleName IS NULL OR r.RULE_NAME LIKE CONCAT('%', :ruleName, '%'))
        AND (:businessRuleName IS NULL OR bri.RULE_NAME LIKE CONCAT('%', :businessRuleName, '%'))
        AND (:orgName IS NULL OR oi.org_name LIKE CONCAT('%', :orgName, '%'))
        AND (:systemList IS NULL OR r.SYSTEM_NAME IN (:systemList))
        AND (:assignedToList IS NULL OR i.ASSIGNED_TO IN (:assignedToList))
        AND (
            :isFixed IS NULL OR 
            (:isFixed = true AND i.CURRENT_STAGE = 'COMPLETED' AND COALESCE(i.LATEST_ERROR_ROW_COUNT, 0) = 0) OR
            (:isFixed = false AND NOT (i.CURRENT_STAGE = 'COMPLETED' AND COALESCE(i.LATEST_ERROR_ROW_COUNT, 0) = 0))
        )
    """
    )
    fun searchIssuesCount(
        @Param("taskId") taskId: Long?,
        @Param("taskName") taskName: String?,
        @Param("systemList") systemList: List<String>?,
        @Param("ruleName") ruleName: String?,
        @Param("businessRuleName") businessRuleName: String?,
        @Param("orgName") orgName: String?,
        @Param("assignedToList") assignedToList: List<String>?,
        @Param("isFixed") isFixed: Boolean?,
    ): Long

    /**
     * 根据条件搜索数据质量问题并按规则ID分组的记录数
     *
     * @param taskName 校验任务名称（模糊搜索）
     * @param systemList 所属系统（多选）
     * @param ruleName 技术规则名称（模糊搜索）
     * @param businessRuleName 业务规则名称（模糊搜索）
     * @param orgName 组织名称（模糊搜索）
     * @param assignedTo 负责人（多选）
     * @param isFixed 是否已修复
     * @return 按规则ID分组的记录数
     */
    @Query(
        """
        SELECT COUNT(*) FROM (
            SELECT i.RULE_ID 
            FROM data_quality_issues i
            JOIN tech_rule r ON i.RULE_ID = r.ID
            LEFT JOIN business_rule_info bri ON r.BUSINESS_RULE_ID = bri.id
            LEFT JOIN organization_info oi ON bri.ORG_CODE = oi.id
            JOIN validate_task_rule vtr ON r.ID = vtr.RULE_ID
            JOIN validate_task vt ON vtr.TASK_ID = vt.ID
            WHERE (:taskName IS NULL OR vt.TASK_NAME LIKE CONCAT('%', :taskName, '%'))
            AND (:ruleName IS NULL OR r.RULE_NAME LIKE CONCAT('%', :ruleName, '%'))
            AND (:businessRuleName IS NULL OR bri.RULE_NAME LIKE CONCAT('%', :businessRuleName, '%'))
            AND (:orgName IS NULL OR oi.org_name LIKE CONCAT('%', :orgName, '%'))
            AND (:systemList IS NULL OR r.SYSTEM_NAME IN (:systemList))
            AND (:assignedToList IS NULL OR i.ASSIGNED_TO IN (:assignedToList))
            AND (
                :isFixed IS NULL OR 
                (:isFixed = true AND i.CURRENT_STAGE = 'COMPLETED' AND COALESCE(i.LATEST_ERROR_ROW_COUNT, 0) = 0) OR
                (:isFixed = false AND NOT (i.CURRENT_STAGE = 'COMPLETED' AND COALESCE(i.LATEST_ERROR_ROW_COUNT, 0) = 0))
            )
            AND i.UPDATE_AT = (
                SELECT MAX(UPDATE_AT)
                FROM data_quality_issues sub
                WHERE sub.RULE_ID = i.RULE_ID
            )
            GROUP BY i.RULE_ID
        ) AS grouped_rules_count
    """
    )
    fun searchIssuesCountGroupByRule(
        @Param("taskName") taskName: String?,
        @Param("systemList") systemList: List<String>?,
        @Param("ruleName") ruleName: String?,
        @Param("businessRuleName") businessRuleName: String?,
        @Param("orgName") orgName: String?,
        @Param("assignedToList") assignedToList: List<String>?,
        @Param("isFixed") isFixed: Boolean?,
    ): Long

    /**
     * 根据条件（包含 taskId）搜索数据质量问题，按 ruleId 取该 task 下最新 updatedAt 一条
     * 并支持分页与排序
     */
    @Query(
        """
        SELECT distinct i.* FROM data_quality_issues i
        JOIN tech_rule r ON i.RULE_ID = r.ID
        LEFT JOIN business_rule_info bri ON r.BUSINESS_RULE_ID = bri.id
        LEFT JOIN organization_info oi ON bri.ORG_CODE = oi.id
        JOIN validate_task_rule vtr ON r.ID = vtr.RULE_ID
        JOIN validate_task vt ON vtr.TASK_ID = vt.ID
        WHERE vt.ID = :taskId
        AND (:taskName IS NULL OR vt.TASK_NAME LIKE CONCAT('%', :taskName, '%'))
        AND (:ruleName IS NULL OR r.RULE_NAME LIKE CONCAT('%', :ruleName, '%'))
        AND (:businessRuleName IS NULL OR bri.RULE_NAME LIKE CONCAT('%', :businessRuleName, '%'))
        AND (:orgName IS NULL OR oi.org_name LIKE CONCAT('%', :orgName, '%'))
        AND (:systemList IS NULL OR r.SYSTEM_NAME IN (:systemList))
        AND (:assignedToList IS NULL OR i.ASSIGNED_TO IN (:assignedToList))
        AND (
            :isFixed IS NULL OR 
            (:isFixed = true AND i.CURRENT_STAGE = 'COMPLETED' AND COALESCE(i.LATEST_ERROR_ROW_COUNT, 0) = 0) OR
            (:isFixed = false AND NOT (i.CURRENT_STAGE = 'COMPLETED' AND COALESCE(i.LATEST_ERROR_ROW_COUNT, 0) = 0))
        )
        AND i.UPDATE_AT = (
            SELECT MAX(sub.UPDATE_AT)
            FROM data_quality_issues sub
            JOIN validate_task_rule vtr2 ON sub.RULE_ID = vtr2.RULE_ID
            JOIN validate_task vt2 ON vtr2.TASK_ID = vt2.ID
            WHERE vt2.ID = :taskId AND sub.RULE_ID = i.RULE_ID
        )
        ORDER BY 
            CASE WHEN :sortField = 'remainingCount' AND :sortOrder = 'ASC' THEN i.LATEST_ERROR_ROW_COUNT END ASC,
            CASE WHEN :sortField = 'remainingCount' AND :sortOrder = 'DESC' THEN i.LATEST_ERROR_ROW_COUNT END DESC,
            i.UPDATE_AT DESC
        LIMIT :limit OFFSET :offset
        """
    )
    fun searchIssuesGroupedByRuleIdWithTaskId(
        @Param("taskId") taskId: Long,
        @Param("taskName") taskName: String?,
        @Param("systemList") systemList: List<String>?,
        @Param("ruleName") ruleName: String?,
        @Param("businessRuleName") businessRuleName: String?,
        @Param("orgName") orgName: String?,
        @Param("assignedToList") assignedToList: List<String>?,
        @Param("isFixed") isFixed: Boolean?,
        @Param("sortField") sortField: String?,
        @Param("sortOrder") sortOrder: String?,
        @Param("limit") limit: Int,
        @Param("offset") offset: Int,
    ): List<IssueEntity>

    /**
     * 计算（包含 taskId）且按 ruleId 取该 task 下最新记录后的总数
     */
    @Query(
        """
        SELECT COUNT(*) FROM (
            SELECT i.RULE_ID
            FROM data_quality_issues i
            JOIN tech_rule r ON i.RULE_ID = r.ID
            LEFT JOIN business_rule_info bri ON r.BUSINESS_RULE_ID = bri.id
            LEFT JOIN organization_info oi ON bri.ORG_CODE = oi.id
            JOIN validate_task_rule vtr ON r.ID = vtr.RULE_ID
            JOIN validate_task vt ON vtr.TASK_ID = vt.ID
            WHERE vt.ID = :taskId
            AND (:taskName IS NULL OR vt.TASK_NAME LIKE CONCAT('%', :taskName, '%'))
            AND (:ruleName IS NULL OR r.RULE_NAME LIKE CONCAT('%', :ruleName, '%'))
            AND (:businessRuleName IS NULL OR bri.RULE_NAME LIKE CONCAT('%', :businessRuleName, '%'))
            AND (:orgName IS NULL OR oi.org_name LIKE CONCAT('%', :orgName, '%'))
            AND (:systemList IS NULL OR r.SYSTEM_NAME IN (:systemList))
            AND (:assignedToList IS NULL OR i.ASSIGNED_TO IN (:assignedToList))
            AND (
                :isFixed IS NULL OR 
                (:isFixed = true AND i.CURRENT_STAGE = 'COMPLETED' AND COALESCE(i.LATEST_ERROR_ROW_COUNT, 0) = 0) OR
                (:isFixed = false AND NOT (i.CURRENT_STAGE = 'COMPLETED' AND COALESCE(i.LATEST_ERROR_ROW_COUNT, 0) = 0))
            )
            AND i.UPDATE_AT = (
                SELECT MAX(sub.UPDATE_AT)
                FROM data_quality_issues sub
                JOIN validate_task_rule vtr2 ON sub.RULE_ID = vtr2.RULE_ID
                JOIN validate_task vt2 ON vtr2.TASK_ID = vt2.ID
                WHERE vt2.ID = :taskId AND sub.RULE_ID = i.RULE_ID
            )
            GROUP BY i.RULE_ID
        ) AS grouped_rules_count
        """
    )
    fun searchIssuesCountGroupByRuleWithTaskId(
        @Param("taskId") taskId: Long,
        @Param("taskName") taskName: String?,
        @Param("systemList") systemList: List<String>?,
        @Param("ruleName") ruleName: String?,
        @Param("businessRuleName") businessRuleName: String?,
        @Param("orgName") orgName: String?,
        @Param("assignedToList") assignedToList: List<String>?,
        @Param("isFixed") isFixed: Boolean?,
    ): Long

}
