package com.datayes.spring

import com.datayes.domain.*
import com.fasterxml.jackson.databind.ObjectMapper
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.apache.poi.xssf.streaming.SXSSFWorkbook
import org.slf4j.LoggerFactory
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

@RestController
@RequestMapping("/api/rule-execution-results")
@Tag(name = "规则执行结果管理", description = "规则执行结果的CRUD操作")
class RuleResultController(
    private val ruleResultRepository: RuleResultRepository,
    private val ruleResultRepositoryImpl: RuleResultRepositoryImpl,
    private val issueRunHistoryRepository: IssueRunHistoryRepository,
    private val objectMapper: ObjectMapper,
    private val jdbcTemplate: JdbcTemplate,
) {

    private val log = LoggerFactory.getLogger(RuleResultController::class.java)

    private fun getExcelMaxRowsPerFile(): Int {
        return try {
            jdbcTemplate.queryForObject(
                "SELECT config_value FROM system_config WHERE config_key = ? AND active_flag = 1",
                String::class.java,
                "export.excel.max_rows_per_file"
            )?.toIntOrNull() ?: 50000
        } catch (e: Exception) {
            log.warn("Failed to read excel max rows per file config, using default 50000", e)
            50000
        }
    }

    @GetMapping
    @Operation(summary = "获取所有规则执行结果", description = "返回所有规则执行结果的列表")
    @ApiResponse(responseCode = "200", description = "成功获取规则执行结果列表")
    fun findAll(): StandardResponse<List<RuleResultEntity>> {
        log.info("d0e1f2g3 | findAll | 开始 | 查询所有规则执行结果")
        val startTime = System.currentTimeMillis()

        try {
            val results = ruleResultRepository.findAll().toList()
            val elapsedTime = System.currentTimeMillis() - startTime
            log.info(
                "d0e1f2g3 | findAll | 完成 | 查询所有规则执行结果成功 | count: {} | 耗时: {}ms",
                results.size,
                elapsedTime
            )
            return StandardResponse.success(payload = results)
        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error(
                "d0e1f2g3 | findAll | 异常 | 查询所有规则执行结果失败 | 耗时: {}ms | 异常: {}",
                elapsedTime,
                e.message,
                e
            )
            throw e
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "通过ID获取规则执行结果", description = "返回指定ID的规则执行结果")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "成功获取规则执行结果")])
    fun findById(
        @Parameter(description = "要检索的规则执行结果ID", required = true)
        @PathVariable id: Long,
    ): StandardResponse<RuleResultEntity> {
        log.info("d0e1f2g3 | findById | 开始 | 查询规则执行结果 | id: {}", id)
        val startTime = System.currentTimeMillis()

        try {
            val result = ruleResultRepository.findById(id)
            val elapsedTime = System.currentTimeMillis() - startTime

            return if (result.isPresent) {
                log.info("d0e1f2g3 | findById | 完成 | 查询规则执行结果成功 | id: {} | 耗时: {}ms", id, elapsedTime)
                StandardResponse.success(payload = result.get())
            } else {
                log.warn(
                    "d0e1f2g3 | findById | 完成 | 未找到指定ID的规则执行结果 | id: {} | 耗时: {}ms",
                    id,
                    elapsedTime
                )
                StandardResponse.failure("未找到ID为${id}的规则执行结果")
            }
        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error(
                "d0e1f2g3 | findById | 异常 | 查询规则执行结果失败 | id: {} | 耗时: {}ms | 异常: {}",
                id, elapsedTime, e.message, e
            )
            throw e
        }
    }

    @PostMapping("/rule/query")
    @Operation(summary = "通过规则ID获取执行结果", description = "返回指定规则ID的执行结果，支持分页和JSON字段过滤")
    fun queryResultsByRuleId(
        @RequestBody request: QueryResultsByRuleIdRequest
    ): StandardResponse<PagedResponse<RuleResultEntity>> {
        log.info(
            "45d2907b | queryResultsByRuleId | 开始 | 通过规则ID获取执行结果 | ruleId: {} | page: {} | size: {}",
            request.ruleId,
            request.page,
            request.size
        )
        val startTime = System.currentTimeMillis()

        try {
            val offset = (request.page - 1) * request.size
            log.debug("45d2907b | queryResultsByRuleId | 计算分页参数 | offset: {} | size: {}", offset, request.size)

            // 使用请求体中的过滤条件
            val filterList = request.filters
            log.debug("45d2907b | queryResultsByRuleId | 使用请求体过滤参数 | filterList: {}", filterList)
            log.debug("45d2907b | queryResultsByRuleId | 新增过滤参数 | isFixed: {} | assignmentStatus: {} | softwareDevReqNumber: {} | issueCauseType: {} | issueCauseDetail: {}", 
                request.isFixed, request.assignmentStatus, request.softwareDevReqNumber, request.issueCauseType, request.issueCauseDetail)

            // 查找最新的规则执行结果，获取runId
            log.debug("45d2907b | queryResultsByRuleId | 查找最新的规则执行结果 | ruleId: {}", request.ruleId)
            val latestRuleResult = ruleResultRepository.findTopByRuleIdOrderByCreatedAtDesc(request.ruleId)
            val runId = latestRuleResult?.runId

            // 检查是否有任何过滤条件（包括新增的过滤条件）
            val hasAnyFilter = filterList.isNotEmpty() || 
                               request.isFixed != null || 
                               !request.assignmentStatus.isNullOrBlank() ||
                               !request.softwareDevReqNumber.isNullOrBlank() || 
                               !request.issueCauseType.isNullOrBlank() || 
                               !request.issueCauseDetail.isNullOrBlank()

            val results = if (!hasAnyFilter) {
                // 无过滤条件，使用现有逻辑
                if (runId.isNullOrBlank()) {
                    log.debug(
                        "45d2907b | queryResultsByRuleId | 未找到最新runId，查询所有规则执行结果 | ruleId: {}",
                        request.ruleId
                    )
                    ruleResultRepositoryImpl.findAllByRuleIdOrderByUpdatedAtDescWithPagination(
                        request.ruleId,
                        request.size,
                        offset
                    )
                } else {
                    log.debug(
                        "45d2907b | queryResultsByRuleId | 使用最新runId查询规则执行结果 | ruleId: {} | runId: {}",
                        request.ruleId,
                        runId
                    )
                    ruleResultRepositoryImpl.findByRuleIdAndRunIdOrderByUpdatedAtDescWithPagination(
                        request.ruleId,
                        runId,
                        request.size,
                        offset
                    )
                }
            } else {
                // 有过滤条件，使用新的增强过滤方法
                log.debug(
                    "45d2907b | queryResultsByRuleId | 使用增强过滤条件查询规则执行结果 | ruleId: {} | runId: {} | filters: {} | isFixed: {} | assignmentStatus: {} | softwareDevReqNumber: {} | issueCauseType: {} | issueCauseDetail: {}",
                    request.ruleId, runId, filterList, request.isFixed, request.assignmentStatus, request.softwareDevReqNumber, request.issueCauseType, request.issueCauseDetail
                )
                ruleResultRepositoryImpl.findWithEnhancedFiltersOrderByUpdatedAtDescWithPagination(
                    request.ruleId,
                    runId,
                    filterList,
                    request.isFixed,
                    request.assignmentStatus,
                    request.softwareDevReqNumber,
                    request.issueCauseType,
                    request.issueCauseDetail,
                    request.size,
                    offset
                )
            }

            // 查询总数
            val total = if (!hasAnyFilter) {
                // 无过滤条件，使用现有计数逻辑
                if (runId.isNullOrBlank()) {
                    ruleResultRepository.countByRuleId(request.ruleId)
                } else {
                    ruleResultRepository.countByRuleIdAndRunId(request.ruleId, runId)
                }
            } else {
                // 有过滤条件，使用新的增强计数方法
                ruleResultRepositoryImpl.countWithEnhancedFilters(
                    request.ruleId,
                    runId,
                    filterList,
                    request.isFixed,
                    request.assignmentStatus,
                    request.softwareDevReqNumber,
                    request.issueCauseType,
                    request.issueCauseDetail
                )
            }

            val totalPages = if (request.size > 0) ((total + request.size - 1) / request.size).toInt() else 0
            log.debug("45d2907b | queryResultsByRuleId | 查询总数完成 | total: {} | totalPages: {}", total, totalPages)

            val result = PagedResponse(results, request.page, request.size, total, totalPages)

            val elapsedTime = System.currentTimeMillis() - startTime
            log.info(
                "45d2907b | queryResultsByRuleId | 完成 | 通过规则ID获取执行结果成功 | ruleId: {} | runId: {} | count: {} | total: {} | 耗时: {}ms",
                request.ruleId, runId, results.size, total, elapsedTime
            )

            return StandardResponse.success(payload = result)
        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error(
                "45d2907b | queryResultsByRuleId | 异常 | 通过规则ID获取执行结果失败 | ruleId: {} | 耗时: {}ms | 异常: {}",
                request.ruleId, elapsedTime, e.message, e
            )
            throw e
        }
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "创建规则执行结果", description = "创建新的规则执行结果")
    @ApiResponse(responseCode = "201", description = "成功创建规则执行结果")
    fun create(@RequestBody ruleResult: RuleResultEntity): StandardResponse<RuleResultEntity> {
        log.info("create | 创建规则执行结果 | ruleId: ${ruleResult.ruleId}")
        val savedResult = ruleResultRepository.save(ruleResult)
        return StandardResponse.success(message = "规则执行结果创建成功", payload = savedResult)
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新规则执行结果", description = "更新指定ID的规则执行结果")
    @ApiResponse(responseCode = "200", description = "成功更新规则执行结果")
    fun update(
        @Parameter(description = "要更新的规则执行结果ID", required = true)
        @PathVariable id: Long,
        @RequestBody ruleResult: RuleResultEntity,
    ): StandardResponse<RuleResultEntity> {
        log.info("update | 更新规则执行结果 | id: $id")

        if (!ruleResultRepository.existsById(id)) {
            return StandardResponse.failure("未找到ID为${id}的规则执行结果")
        }

        // 确保ID是正确的
        val updatedResult = if (ruleResult.id != id) {
            // 创建一个新的实例，确保ID匹配
            ruleResult.copy(
                id = id,
                updatedAt = LocalDateTime.now()
            )
        } else {
            // 如果ID已经正确，只更新updatedAt
            ruleResult.copy(updatedAt = LocalDateTime.now())
        }

        val savedResult = ruleResultRepository.save(updatedResult)
        return StandardResponse.success(message = "规则执行结果更新成功", payload = savedResult)
    }

    @PatchMapping("/{id}/fix")
    @Operation(summary = "将规则执行结果标记为已修复", description = "将规则执行结果的状态更新为已修复")
    @ApiResponse(responseCode = "200", description = "成功标记规则执行结果为已修复")
    fun markAsFixed(
        @Parameter(description = "要标记为已修复的规则执行结果ID", required = true)
        @PathVariable id: Long,
        @RequestParam(required = false) updatedBy: String?,
    ): StandardResponse<RuleResultEntity> {
        log.info("markAsFixed | 标记规则执行结果为已修复 | id: $id | updatedBy: $updatedBy")

        val ruleResultOptional = ruleResultRepository.findById(id)
        if (!ruleResultOptional.isPresent) {
            return StandardResponse.failure("未找到ID为${id}的规则执行结果")
        }

        val ruleResult = ruleResultOptional.get()
        val updatedResult = ruleResult.copy(
            isFixed = 1, // 1表示已修复
            updatedBy = updatedBy ?: ruleResult.updatedBy,
            updatedAt = LocalDateTime.now()
        )

        val savedResult = ruleResultRepository.save(updatedResult)
        return StandardResponse.success(message = "规则执行结果已标记为已修复", payload = savedResult)
    }

    @PostMapping("/software-dev-req-number")
    @Operation(summary = "更新软开需求编号", description = "批量更新软开需求编号，支持插入或更新操作")
    @ApiResponse(responseCode = "200", description = "成功更新软开需求编号")
    fun updateSoftwareDevReqNumber(
        @RequestBody request: UpdateSoftwareDevReqNumberRequest,
    ): StandardResponse<List<RuleResultEntity>> {
        log.info("updateSoftwareDevReqNumber | 更新软开需求编号 | ids: {} | ruleId: {} | operationType: {} | newSoftwareDevReqNumber: {}", 
                request.ids, request.ruleId, request.operationType, request.newSoftwareDevReqNumber)

        if (request.ids.isEmpty() && request.ruleId == null) {
            return StandardResponse.failure("ID列表和规则ID不能同时为空")
        }

        if (request.newSoftwareDevReqNumber.isBlank()) {
            return StandardResponse.failure("软开需求编号不能为空")
        }

        val updatedResults = mutableListOf<RuleResultEntity>()
        
        // Get target IDs - either from request.ids or by ruleId
        val targetIds = request.ids.ifEmpty {
            val ruleId = request.ruleId ?: return StandardResponse.failure("规则ID不能为空")
            // Find all IDs by ruleId
            val ruleResults = ruleResultRepository.findAllByRuleId(ruleId)
            ruleResults.mapNotNull { result ->
                result.id ?: run {
                    log.warn("updateSoftwareDevReqNumber | 发现没有ID的规则执行结果 | ruleId: {}", ruleId)
                    null
                }
            }
        }
        
        for (id in targetIds) {
            val ruleResultOptional = ruleResultRepository.findById(id)
            if (!ruleResultOptional.isPresent) {
                log.warn("updateSoftwareDevReqNumber | 未找到ID为{}的规则执行结果", id)
                continue
            }

            val ruleResult = ruleResultOptional.get()
            
            // Handle software dev req number list logic
            val updatedSoftwareDevReqNumberList = try {
                val currentList = if (ruleResult.softwareDevReqNumberList.isNullOrBlank()) {
                    mutableListOf()
                } else {
                    objectMapper.readValue(ruleResult.softwareDevReqNumberList, Array<String>::class.java).toMutableList()
                }
                
                when (request.operationType.uppercase()) {
                    "INSERT" -> {
                        // Insert: add to list if original is null/empty, or always add based on requirement
                        currentList.add(request.newSoftwareDevReqNumber.trim())
                        objectMapper.writeValueAsString(currentList)
                    }
                    "UPDATE" -> {
                        // Update: update the last value in the existing list, or insert if list is empty
                        if (currentList.isEmpty()) {
                            // If list is empty, insert the new value
                            currentList.add(request.newSoftwareDevReqNumber.trim())
                        } else {
                            // Update the last value in the list
                            currentList[currentList.size - 1] = request.newSoftwareDevReqNumber.trim()
                        }
                        objectMapper.writeValueAsString(currentList)
                    }
                    else -> {
                        log.warn("updateSoftwareDevReqNumber | 不支持的操作类型: {}", request.operationType)
                        ruleResult.softwareDevReqNumberList
                    }
                }
            } catch (e: Exception) {
                log.warn("updateSoftwareDevReqNumber | JSON解析失败，创建新列表 | softwareDevReqNumberList: {} | 异常: {}", 
                    ruleResult.softwareDevReqNumberList, e.message)
                objectMapper.writeValueAsString(listOf(request.newSoftwareDevReqNumber.trim()))
            }
            
            // Auto-determine assignment status based on whether software dev req number list has values
            val newAssignmentStatus = if (!updatedSoftwareDevReqNumberList.isNullOrBlank() && 
                                         updatedSoftwareDevReqNumberList != "[]" && 
                                         updatedSoftwareDevReqNumberList != "null") {
                "已分配"
            } else {
                "未分配"
            }
            
            val updatedResult = ruleResult.copy(
                softwareDevReqNumberList = updatedSoftwareDevReqNumberList,
                assignmentStatus = newAssignmentStatus,
                updatedBy = request.updatedBy ?: ruleResult.updatedBy,
                updatedAt = LocalDateTime.now()
            )

            val savedResult = ruleResultRepository.save(updatedResult)
            updatedResults.add(savedResult)
        }

        val message = if (request.ids.isNotEmpty()) {
            "软开需求编号更新成功，成功更新${updatedResults.size}条记录"
        } else {
            "软开需求编号更新成功，规则ID: ${request.ruleId}，成功更新${updatedResults.size}条记录"
        }
        return StandardResponse.success(message = message, payload = updatedResults)
    }

    @PostMapping("/issue-cause")
    @Operation(summary = "更新问题原因信息", description = "批量更新问题原因类型和问题原因详述")
    @ApiResponse(responseCode = "200", description = "成功更新问题原因信息")
    fun updateIssueCause(
        @RequestBody request: UpdateIssueCauseRequest,
    ): StandardResponse<List<RuleResultEntity>> {
        log.info("updateIssueCause | 更新问题原因信息 | ids: {} | ruleId: {} | issueCauseType: {} | issueCauseDetail: {}", 
                request.ids, request.ruleId, request.issueCauseType, request.issueCauseDetail)

        if (request.ids.isEmpty() && request.ruleId == null) {
            return StandardResponse.failure("ID列表和规则ID不能同时为空")
        }

        if (request.issueCauseType.isNullOrBlank() && request.issueCauseDetail.isNullOrBlank()) {
            return StandardResponse.failure("问题原因类型和问题原因详述不能同时为空")
        }

        val updatedResults = mutableListOf<RuleResultEntity>()
        
        // Get target IDs - either from request.ids or by ruleId
        val targetIds = request.ids.ifEmpty {
            val ruleId = request.ruleId ?: return StandardResponse.failure("规则ID不能为空")
            // Find all IDs by ruleId
            val ruleResults = ruleResultRepository.findAllByRuleId(ruleId)
            ruleResults.mapNotNull { result ->
                result.id ?: run {
                    log.warn("updateIssueCause | 发现没有ID的规则执行结果 | ruleId: {}", ruleId)
                    null
                }
            }
        }
        
        for (id in targetIds) {
            val ruleResultOptional = ruleResultRepository.findById(id)
            if (!ruleResultOptional.isPresent) {
                log.warn("updateIssueCause | 未找到ID为{}的规则执行结果", id)
                continue
            }

            val ruleResult = ruleResultOptional.get()
            
            val updatedResult = ruleResult.copy(
                issueCauseType = request.issueCauseType ?: ruleResult.issueCauseType,
                issueCauseDetail = request.issueCauseDetail ?: ruleResult.issueCauseDetail,
                updatedBy = request.updatedBy ?: ruleResult.updatedBy,
                updatedAt = LocalDateTime.now()
            )

            val savedResult = ruleResultRepository.save(updatedResult)
            updatedResults.add(savedResult)
        }

        val message = if (request.ids.isNotEmpty()) {
            "问题原因信息更新成功，成功更新${updatedResults.size}条记录"
        } else {
            "问题原因信息更新成功，规则ID: ${request.ruleId}，成功更新${updatedResults.size}条记录"
        }
        return StandardResponse.success(message = message, payload = updatedResults)
    }

    /**
     * 删除指定ID的规则执行结果。
     *
     * @param id 要删除的规则执行结果ID
     * @return 表示操作成功或失败的标准响应，若成功则携带成功消息
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除规则执行结果", description = "删除指定ID的规则执行结果")
    @ApiResponse(responseCode = "200", description = "成功删除规则执行结果")
    @Transactional // 添加事务注解
    fun delete(
        @Parameter(description = "要删除的规则执行结果ID", required = true)
        @PathVariable id: Long,
    ): StandardResponse<Unit> {
        log.info("e2f3g4h5 | delete | 开始 | 删除规则执行结果 | id: {}", id)
        val startTime = System.currentTimeMillis()

        try {
            // 查找运行历史记录
            log.debug("e2f3g4h5 | delete | 查找运行历史记录 | id: {}", id)
            val issueRunHistory = issueRunHistoryRepository.findById(id)

            if (!issueRunHistory.isPresent) {
                val elapsedTime = System.currentTimeMillis() - startTime
                log.warn("e2f3g4h5 | delete | 完成 | 未找到指定ID的规则执行结果 | id: {} | 耗时: {}ms", id, elapsedTime)
                return StandardResponse.failure("未找到ID为${id}的规则执行结果")
            }

            val runId = issueRunHistory.get().runId
            log.debug("e2f3g4h5 | delete | 获取到runId | id: {} | runId: {}", id, runId)

            // 删除运行历史记录
            log.debug("e2f3g4h5 | delete | 删除运行历史记录 | id: {}", id)
            issueRunHistoryRepository.deleteById(id)

            // 查找并删除关联的规则执行结果
            log.debug("e2f3g4h5 | delete | 查找关联的规则执行结果 | runId: {}", runId)
            val findByRunId = ruleResultRepository.findByRunId(runId)
            log.info("e2f3g4h5 | delete | 找到关联的规则执行结果 | runId: {} | count: {}", runId, findByRunId.size)

            // 删除关联的规则执行结果
            log.debug("e2f3g4h5 | delete | 开始删除关联的规则执行结果 | count: {}", findByRunId.size)
            findByRunId.forEach { ruleResultRepository.delete(it) } // 考虑批量删除

            val elapsedTime = System.currentTimeMillis() - startTime
            log.info(
                "e2f3g4h5 | delete | 完成 | 删除规则执行结果成功 | id: {} | runId: {} | 删除关联结果数: {} | 耗时: {}ms",
                id, runId, findByRunId.size, elapsedTime
            )

            return StandardResponse.success(message = "规则执行结果删除成功")
        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error(
                "e2f3g4h5 | delete | 异常 | 删除规则执行结果失败 | id: {} | 耗时: {}ms | 异常: {}",
                id, elapsedTime, e.message, e
            )
            throw e
        }
    }

    // @GetMapping("/export/rule/{ruleId}")
    @Operation(summary = "导出规则执行结果到CSV", description = "导出指定规则的所有执行结果到CSV文件")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "导出成功"),
            ApiResponse(responseCode = "404", description = "规则不存在"),
            ApiResponse(responseCode = "500", description = "服务器错误")
        ]
    )
    fun exportResultsByRuleId(
        @Parameter(description = "规则ID") @PathVariable ruleId: Long
    ): ResponseEntity<StreamingResponseBody> {
        return exportResultsByRuleIdAsCsvStreaming(ruleId)
    }

    @GetMapping("/export/rule/{ruleId}/non-streaming")
    @Operation(summary = "导出规则执行结果到Excel (非流式)", description = "导出指定规则的所有执行结果到Excel文件，使用非流式方式")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "导出成功"),
            ApiResponse(responseCode = "404", description = "规则不存在"),
            ApiResponse(responseCode = "500", description = "服务器错误")
        ]
    )
    fun exportResultsByRuleIdAsExcelNonStreaming(
        @Parameter(description = "规则ID") @PathVariable ruleId: Long
    ): ResponseEntity<ByteArray> {
        log.info("exportResultsByRuleIdAsExcelNonStreaming | 开始 | ruleId: {}", ruleId)
        val startTime = System.currentTimeMillis()

        try {
            // 查询数据总数
            val totalCount = ruleResultRepository.countByRuleId(ruleId)
            if (totalCount == 0L) {
                log.warn("exportResultsByRuleIdAsExcelNonStreaming | 警告 | 未找到数据 | ruleId: {}", ruleId)
                throw IllegalArgumentException("未找到规则 $ruleId 的执行结果数据")
            }

            log.info("exportResultsByRuleIdAsExcelNonStreaming | 查询到数据总数: {} | ruleId: {}", totalCount, ruleId)

            val maxRowsPerFile = 100000 // 每个Excel文件最大行数

            // 决定导出方式
            return if (totalCount <= maxRowsPerFile) {
                // 单个Excel文件，直接返回ByteArray
                exportSingleExcelFileNonStreaming(ruleId, null, totalCount)
            } else {
                // 多个Excel文件打包ZIP
                exportMultipleExcelFilesAsZipNonStreaming(ruleId, null, totalCount, maxRowsPerFile)
            }

        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error("exportResultsByRuleIdAsExcelNonStreaming | 异常 | ruleId: {} | 耗时: {}ms | 异常: {}", ruleId, elapsedTime, e.message, e)
            throw e
        }
    }

    @GetMapping("/export/rule/{ruleId}")
    @Operation(summary = "导出规则执行结果到Excel", description = "导出指定规则的所有执行结果到Excel文件")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "导出成功"),
            ApiResponse(responseCode = "404", description = "规则不存在"),
            ApiResponse(responseCode = "500", description = "服务器错误")
        ]
    )
    fun exportResultsByRuleIdAsExcel(
        @Parameter(description = "规则ID") @PathVariable ruleId: Long
    ): ResponseEntity<ByteArray> {
        log.info("c0adaae6 | exportResultsByRuleId | 开始 | 导出规则执行结果到Excel | ruleId: {}", ruleId)
        val startTime = System.currentTimeMillis()

        try {
            // 查找最新的规则执行结果，获取runId
            log.debug("3eb3ce7d | exportResultsByRuleId | 查找最新的规则执行结果 | ruleId: {}", ruleId)
            val latestRuleResult = ruleResultRepository.findTopByRuleIdOrderByCreatedAtDesc(ruleId)
            val runId = latestRuleResult?.runId

            // 获取总记录数
            val totalCount = if (runId.isNullOrBlank()) {
                ruleResultRepositoryImpl.countByRuleIdForBatch(ruleId)
            } else {
                ruleResultRepositoryImpl.countByRuleIdAndRunIdForBatch(ruleId, runId)
            }

            log.debug("7a3161dc | exportResultsByRuleId | 总记录数: {} | runId: {}", totalCount, runId)

            // 如果没有数据，返回空响应
            if (totalCount == 0L) {
                log.warn("7d270164 | exportResultsByRuleId | 没有找到数据 | ruleId: {} | runId: {}", ruleId, runId)
                throw IllegalArgumentException("未找到规则 $ruleId 的执行结果数据")
            }

            // 读取配置的每个Excel文件最大行数
            val maxRowsPerFile = getExcelMaxRowsPerFile()
            log.info("读取配置的每个Excel文件最大行数: {}", maxRowsPerFile)

            // 判断是否需要分割文件
            val needsSplit = totalCount > maxRowsPerFile

            if (needsSplit) {
                log.info("数据量 {} 超过配置阈值 {}，将分割为多个Excel文件并打包为ZIP", totalCount, maxRowsPerFile)
                return exportMultipleExcelFilesAsZipNonStreaming(ruleId, runId, totalCount, maxRowsPerFile)
            } else {
                log.info("数据量 {} 未超过配置阈值 {}，导出单个Excel文件", totalCount, maxRowsPerFile)
                return exportSingleExcelFileNonStreaming(ruleId, runId, totalCount)
            }

        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error(
                "e7e63892 | exportResultsByRuleId | 异常 | 导出规则执行结果到Excel失败 | ruleId: {} | 耗时: {}ms | 异常: {}",
                ruleId, elapsedTime, e.message, e
            )
            throw e
        }
    }

    private fun exportSingleExcelFileStreaming(ruleId: Long, runId: String?, totalCount: Long): ResponseEntity<StreamingResponseBody> {
        log.info("exportSingleExcelFileStreaming | 开始 | 流式导出单个Excel文件 | ruleId: {} | totalCount: {}", ruleId, totalCount)
        val startTime = System.currentTimeMillis()

        try {
            // 收集JSON字段名
            val jsonKeys = mutableSetOf<String>()
            val sampleSize = minOf(1000, totalCount.toInt())
            val sampleResults = if (runId.isNullOrBlank()) {
                ruleResultRepositoryImpl.findAllByRuleIdOrderByUpdatedAtDescBatch(ruleId, sampleSize, 0)
            } else {
                ruleResultRepositoryImpl.findByRuleIdAndRunIdOrderByUpdatedAtDescBatch(ruleId, runId, sampleSize, 0)
            }

            for (result in sampleResults) {
                try {
                    val jsonNode = objectMapper.readTree(result.rowData)
                    if (jsonNode.isObject) {
                        jsonNode.fieldNames().forEach { fieldName ->
                            jsonKeys.add(fieldName)
                        }
                    }
                } catch (e: Exception) {
                    log.warn("JSON解析失败 | rowData: {} | 异常: {}", result.rowData, e.message)
                }
            }

            val baseHeaders = listOf("ID", "规则ID", "数据库类型", "执行时间", "是否修复", "分配状态", "软开需求编号列表", "问题原因类型", "问题原因详述")
            val sortedJsonKeys = jsonKeys.sorted()
            val columnHeaders = baseHeaders + sortedJsonKeys

            // 创建流式响应
            val streamingResponseBody = StreamingResponseBody { outputStream ->
                try {
                    // 创建临时Excel文件
                    val tempFile = File.createTempFile("rule_results_${ruleId}_", ".xlsx")
                    
                    try {
                        val workbook = SXSSFWorkbook(1000)
                        val sheet = workbook.createSheet("规则执行结果")

                        // 创建表头
                        val headerRow = sheet.createRow(0)
                        for ((index, header) in columnHeaders.withIndex()) {
                            headerRow.createCell(index).setCellValue(header)
                        }

                        // 批量处理数据
                        val batchSize = 1000
                        val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                        var processedCount = 0
                        var currentRowIndex = 1
                        var lastUpdatedAt: LocalDateTime? = null
                        var lastId: Long? = null

                        while (processedCount < totalCount) {
                            val batchResults = if (runId.isNullOrBlank()) {
                                ruleResultRepositoryImpl.findAllByRuleIdOrderByUpdatedAtDescCursor(
                                    ruleId, batchSize, lastUpdatedAt, lastId
                                )
                            } else {
                                ruleResultRepositoryImpl.findByRuleIdAndRunIdOrderByUpdatedAtDescCursor(
                                    ruleId, runId, batchSize, lastUpdatedAt, lastId
                                )
                            }

                            if (batchResults.isEmpty()) break

                            for (result in batchResults) {
                                val row = sheet.createRow(currentRowIndex++)
                                fillExcelRow(row, result, baseHeaders, sortedJsonKeys, dateFormatter)
                            }

                            processedCount += batchResults.size
                            val lastRecord = batchResults.lastOrNull()
                            lastUpdatedAt = lastRecord?.updatedAt
                            lastId = lastRecord?.id
                        }

                        // 设置列宽
                        val standardColumnWidth = 4000
                        for (i in 0 until minOf(columnHeaders.size, 50)) {
                            sheet.setColumnWidth(i, standardColumnWidth)
                        }

                        // 写入临时文件
                        tempFile.outputStream().use { fileOutputStream ->
                            workbook.write(fileOutputStream)
                        }
                        workbook.close()

                        // 流式传输文件内容
                        FileInputStream(tempFile).use { fileInputStream ->
                            fileInputStream.copyTo(outputStream)
                        }
                        outputStream.flush()

                        val elapsedTime = System.currentTimeMillis() - startTime
                        log.info("exportSingleExcelFileStreaming | 完成 | 流式导出Excel成功 | ruleId: {} | count: {} | 耗时: {}ms", ruleId, processedCount, elapsedTime)

                    } finally {
                        if (tempFile.exists()) {
                            tempFile.delete()
                        }
                    }
                } catch (e: Exception) {
                    val elapsedTime = System.currentTimeMillis() - startTime
                    log.error("exportSingleExcelFileStreaming | 异常 | 流式导出Excel失败 | ruleId: {} | 耗时: {}ms | 异常: {}", ruleId, elapsedTime, e.message, e)
                    throw e
                }
            }

            // 设置响应头
            val headers = HttpHeaders()
            headers.contentType = MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            headers.setContentDispositionFormData("attachment", "rule_results_${ruleId}_${System.currentTimeMillis()}.xlsx")
            headers.set("Transfer-Encoding", "chunked")

            return ResponseEntity.ok()
                .headers(headers)
                .body(streamingResponseBody)

        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error("exportSingleExcelFileStreaming | 异常 | 流式导出Excel初始化失败 | ruleId: {} | 耗时: {}ms | 异常: {}", ruleId, elapsedTime, e.message, e)
            throw e
        }
    }

    private fun exportSingleExcelFileNonStreaming(ruleId: Long, runId: String?, totalCount: Long): ResponseEntity<ByteArray> {
        log.info("exportSingleExcelFileNonStreaming | 开始 | 非流式导出单个Excel文件 | ruleId: {} | count: {}", ruleId, totalCount)
        val startTime = System.currentTimeMillis()

        try {
            // 收集JSON字段名
            val jsonKeys = mutableSetOf<String>()
            val sampleSize = minOf(1000, totalCount.toInt())
            val sampleResults = if (runId.isNullOrBlank()) {
                ruleResultRepositoryImpl.findAllByRuleIdOrderByUpdatedAtDescBatch(ruleId, sampleSize, 0)
            } else {
                ruleResultRepositoryImpl.findByRuleIdAndRunIdOrderByUpdatedAtDescBatch(ruleId, runId, sampleSize, 0)
            }

            for (result in sampleResults) {
                try {
                    val jsonNode = objectMapper.readTree(result.rowData)
                    if (jsonNode.isObject) {
                        jsonNode.fieldNames().forEach { fieldName ->
                            jsonKeys.add(fieldName)
                        }
                    }
                } catch (e: Exception) {
                    log.warn("JSON解析失败 | rowData: {} | 异常: {}", result.rowData, e.message)
                }
            }

            if (jsonKeys.isEmpty()) {
                log.warn("未找到JSON字段，将只导出基础列")
            }

            val baseHeaders = listOf("ID", "规则ID", "数据库类型", "执行时间", "是否修复", "分配状态", "软开需求编号列表", "问题原因类型", "问题原因详述")
            val sortedJsonKeys = jsonKeys.sorted()
            val columnHeaders = baseHeaders.plus(sortedJsonKeys)

            // 创建Excel工作簿
            val workbook = SXSSFWorkbook(1000)
            val sheet = workbook.createSheet("规则执行结果")

            // 创建表头
            val headerRow = sheet.createRow(0)
            for ((index, header) in columnHeaders.withIndex()) {
                headerRow.createCell(index).setCellValue(header)
            }

            val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")

            // 分页查询所有数据
            var processedCount = 0
            var lastUpdatedAt: LocalDateTime? = null
            var lastId: Long? = null

            while (processedCount < totalCount) {
                val batchSize = 1000
                val results = if (runId.isNullOrBlank()) {
                    ruleResultRepositoryImpl.findAllByRuleIdOrderByUpdatedAtDescCursor(
                        ruleId, batchSize, lastUpdatedAt, lastId
                    )
                } else {
                    ruleResultRepositoryImpl.findByRuleIdAndRunIdOrderByUpdatedAtDescCursor(
                        ruleId, runId, batchSize, lastUpdatedAt, lastId
                    )
                }

                if (results.isEmpty()) break

                for (result in results) {
                    val row = sheet.createRow(processedCount + 1)
                    fillExcelRow(row, result, baseHeaders, sortedJsonKeys, dateFormatter)
                    processedCount++
                    lastUpdatedAt = result.updatedAt
                    lastId = result.id
                }

                if (processedCount % 5000 == 0) {
                    log.info("已处理 {} / {} 条记录", processedCount, totalCount)
                }
            }

            // 将Excel写入ByteArray
            val byteArrayOutputStream = ByteArrayOutputStream()
            workbook.write(byteArrayOutputStream)
            workbook.close()

            val elapsedTime = System.currentTimeMillis() - startTime
            log.info("exportSingleExcelFileNonStreaming | 完成 | 非流式导出Excel成功 | ruleId: {} | count: {} | 耗时: {}ms", ruleId, processedCount, elapsedTime)

            // 设置响应头
            val headers = HttpHeaders()
            headers.contentType = MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            headers.setContentDispositionFormData("attachment", "rule_results_${ruleId}_${System.currentTimeMillis()}.xlsx")

            return ResponseEntity.ok()
                .headers(headers)
                .body(byteArrayOutputStream.toByteArray())

        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error("exportSingleExcelFileNonStreaming | 异常 | 非流式导出Excel失败 | ruleId: {} | 耗时: {}ms | 异常: {}", ruleId, elapsedTime, e.message, e)
            throw e
        }
    }

    private fun exportMultipleExcelFilesAsZipNonStreaming(ruleId: Long, runId: String?, totalCount: Long, maxRowsPerFile: Int): ResponseEntity<ByteArray> {
        log.info("exportMultipleExcelFilesAsZipNonStreaming | 开始 | 非流式导出多个Excel文件并压缩 | ruleId: {} | totalCount: {} | maxRowsPerFile: {}", ruleId, totalCount, maxRowsPerFile)
        val startTime = System.currentTimeMillis()

        try {
            // 计算需要的文件数量
            val fileCount = ((totalCount + maxRowsPerFile - 1) / maxRowsPerFile).toInt()
            log.info("需要分割为 {} 个Excel文件", fileCount)

            // 收集JSON字段名
            val jsonKeys = mutableSetOf<String>()
            val sampleSize = minOf(1000, totalCount.toInt())
            val sampleResults = if (runId.isNullOrBlank()) {
                ruleResultRepositoryImpl.findAllByRuleIdOrderByUpdatedAtDescBatch(ruleId, sampleSize, 0)
            } else {
                ruleResultRepositoryImpl.findByRuleIdAndRunIdOrderByUpdatedAtDescBatch(ruleId, runId, sampleSize, 0)
            }

            for (result in sampleResults) {
                try {
                    val jsonNode = objectMapper.readTree(result.rowData)
                    if (jsonNode.isObject) {
                        jsonNode.fieldNames().forEach { fieldName ->
                            jsonKeys.add(fieldName)
                        }
                    }
                } catch (e: Exception) {
                    log.warn("JSON解析失败 | rowData: {} | 异常: {}", result.rowData, e.message)
                }
            }

            if (jsonKeys.isEmpty()) {
                log.warn("未找到JSON字段，将只导出基础列")
            }

            val baseHeaders = listOf("ID", "规则ID", "数据库类型", "执行时间", "是否修复", "分配状态", "软开需求编号列表", "问题原因类型", "问题原因详述")
            val sortedJsonKeys = jsonKeys.sorted()
            val columnHeaders = baseHeaders.plus(sortedJsonKeys)

            // 创建ByteArrayOutputStream来收集ZIP数据
            val byteArrayOutputStream = ByteArrayOutputStream()
            ZipOutputStream(byteArrayOutputStream).use { zipStream ->
                val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                var totalProcessedCount = 0
                var lastUpdatedAt: LocalDateTime? = null
                var lastId: Long? = null

                for (fileIndex in 1..fileCount) {
                    log.info("正在处理第 {} / {} 个Excel文件", fileIndex, fileCount)

                    // 创建当前文件的Excel（内存中）
                    val workbook = SXSSFWorkbook(1000)
                    val sheet = workbook.createSheet("规则执行结果-第${fileIndex}部分")

                    // 创建表头
                    val headerRow = sheet.createRow(0)
                    for ((index, header) in columnHeaders.withIndex()) {
                        headerRow.createCell(index).setCellValue(header)
                    }

                    // 处理当前文件的数据
                    var currentFileProcessedCount = 0
                    var currentRowIndex = 1
                    val batchSize = 1000

                    while (currentFileProcessedCount < maxRowsPerFile && totalProcessedCount < totalCount) {
                        val remainingInFile = maxRowsPerFile - currentFileProcessedCount
                        val currentBatchSize = minOf(batchSize, remainingInFile)

                        val batchResults = if (runId.isNullOrBlank()) {
                            ruleResultRepositoryImpl.findAllByRuleIdOrderByUpdatedAtDescCursor(
                                ruleId, currentBatchSize, lastUpdatedAt, lastId
                            )
                        } else {
                            ruleResultRepositoryImpl.findByRuleIdAndRunIdOrderByUpdatedAtDescCursor(
                                ruleId, runId, currentBatchSize, lastUpdatedAt, lastId
                            )
                        }

                        if (batchResults.isEmpty()) break

                        for (result in batchResults) {
                            val row = sheet.createRow(currentRowIndex++)
                            fillExcelRow(row, result, baseHeaders, sortedJsonKeys, dateFormatter)
                        }

                        currentFileProcessedCount += batchResults.size
                        totalProcessedCount += batchResults.size

                        val lastRecord = batchResults.lastOrNull()
                        lastUpdatedAt = lastRecord?.updatedAt
                        lastId = lastRecord?.id
                    }


                    // 将当前Excel添加到ZIP
                    val currentExcelFile = File.createTempFile("rule_results_${ruleId}_part${fileIndex}_", ".xlsx")
                    try {
                        FileOutputStream(currentExcelFile).use { fileOutputStream ->
                            workbook.write(fileOutputStream)
                        }
                        workbook.close()

                        // 添加到ZIP
                        zipStream.putNextEntry(ZipEntry("规则执行结果_${ruleId}_第${fileIndex}部分.xlsx"))
                        FileInputStream(currentExcelFile).use { fileInputStream ->
                            fileInputStream.copyTo(zipStream)
                        }
                        zipStream.closeEntry()

                        log.info("第 {} 个Excel文件处理完成，包含 {} 条记录", fileIndex, currentFileProcessedCount)

                    } finally {
                        // 清理临时文件
                        if (currentExcelFile.exists()) {
                            currentExcelFile.delete()
                        }
                    }
                }

                val elapsedTime = System.currentTimeMillis() - startTime
                log.info("exportMultipleExcelFilesAsZipNonStreaming | 完成 | 非流式导出ZIP成功 | ruleId: {} | fileCount: {} | totalCount: {} | 耗时: {}ms", ruleId, fileCount, totalCount, elapsedTime)
            }

            // 设置响应头
            val headers = HttpHeaders()
            headers.contentType = MediaType.parseMediaType("application/zip")
            headers.setContentDispositionFormData("attachment", "rule_results_${ruleId}_${System.currentTimeMillis()}.zip")

            return ResponseEntity.ok()
                .headers(headers)
                .body(byteArrayOutputStream.toByteArray())

        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error("exportMultipleExcelFilesAsZipNonStreaming | 异常 | 非流式导出ZIP失败 | ruleId: {} | 耗时: {}ms | 异常: {}", ruleId, elapsedTime, e.message, e)
            throw e
        }
    }

    private fun exportMultipleExcelFilesAsZipStreaming(ruleId: Long, runId: String?, totalCount: Long, maxRowsPerFile: Int): ResponseEntity<StreamingResponseBody> {
        log.info("exportMultipleExcelFilesAsZipStreaming | 开始 | 流式导出多个Excel文件并压缩 | ruleId: {} | totalCount: {} | maxRowsPerFile: {}", ruleId, totalCount, maxRowsPerFile)
        val startTime = System.currentTimeMillis()

        try {
            // 计算需要的文件数量
            val fileCount = ((totalCount + maxRowsPerFile - 1) / maxRowsPerFile).toInt()
            log.info("需要分割为 {} 个Excel文件", fileCount)

            // 收集JSON字段名
            val jsonKeys = mutableSetOf<String>()
            val sampleSize = minOf(1000, totalCount.toInt())
            val sampleResults = if (runId.isNullOrBlank()) {
                ruleResultRepositoryImpl.findAllByRuleIdOrderByUpdatedAtDescBatch(ruleId, sampleSize, 0)
            } else {
                ruleResultRepositoryImpl.findByRuleIdAndRunIdOrderByUpdatedAtDescBatch(ruleId, runId, sampleSize, 0)
            }

            for (result in sampleResults) {
                try {
                    val jsonNode = objectMapper.readTree(result.rowData)
                    if (jsonNode.isObject) {
                        jsonNode.fieldNames().forEach { fieldName ->
                            jsonKeys.add(fieldName)
                        }
                    }
                } catch (e: Exception) {
                    log.warn("JSON解析失败 | rowData: {} | 异常: {}", result.rowData, e.message)
                }
            }

            val baseHeaders = listOf("ID", "规则ID", "数据库类型", "执行时间", "是否修复", "分配状态", "软开需求编号列表", "问题原因类型", "问题原因详述")
            val sortedJsonKeys = jsonKeys.sorted()
            val columnHeaders = baseHeaders + sortedJsonKeys

            // 创建流式响应
            val streamingResponseBody = StreamingResponseBody { outputStream ->
                try {
                    ZipOutputStream(outputStream).use { zipStream ->
                        val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                        var totalProcessedCount = 0
                        var lastUpdatedAt: LocalDateTime? = null
                        var lastId: Long? = null

                        for (fileIndex in 1..fileCount) {
                            log.info("正在处理第 {} / {} 个Excel文件", fileIndex, fileCount)

                            // 创建当前文件的Excel（临时文件）
                            val currentExcelFile = File.createTempFile("rule_results_${ruleId}_part${fileIndex}_", ".xlsx")
                            
                            try {
                                val workbook = SXSSFWorkbook(1000)
                                val sheet = workbook.createSheet("规则执行结果-第${fileIndex}部分")

                                // 创建表头
                                val headerRow = sheet.createRow(0)
                                for ((index, header) in columnHeaders.withIndex()) {
                                    headerRow.createCell(index).setCellValue(header)
                                }

                                // 处理当前文件的数据
                                var currentFileProcessedCount = 0
                                var currentRowIndex = 1
                                val batchSize = 1000

                                while (currentFileProcessedCount < maxRowsPerFile && totalProcessedCount < totalCount) {
                                    val remainingInFile = maxRowsPerFile - currentFileProcessedCount
                                    val currentBatchSize = minOf(batchSize, remainingInFile)

                                    val batchResults = if (runId.isNullOrBlank()) {
                                        ruleResultRepositoryImpl.findAllByRuleIdOrderByUpdatedAtDescCursor(
                                            ruleId, currentBatchSize, lastUpdatedAt, lastId
                                        )
                                    } else {
                                        ruleResultRepositoryImpl.findByRuleIdAndRunIdOrderByUpdatedAtDescCursor(
                                            ruleId, runId, currentBatchSize, lastUpdatedAt, lastId
                                        )
                                    }

                                    if (batchResults.isEmpty()) break

                                    for (result in batchResults) {
                                        val row = sheet.createRow(currentRowIndex++)
                                        fillExcelRow(row, result, baseHeaders, sortedJsonKeys, dateFormatter)
                                    }

                                    currentFileProcessedCount += batchResults.size
                                    totalProcessedCount += batchResults.size

                                    val lastRecord = batchResults.lastOrNull()
                                    lastUpdatedAt = lastRecord?.updatedAt
                                    lastId = lastRecord?.id
                                }

                                // 设置列宽
                                val standardColumnWidth = 4000
                                for (i in 0 until minOf(columnHeaders.size, 50)) {
                                    sheet.setColumnWidth(i, standardColumnWidth)
                                }

                                // 保存Excel文件到临时文件
                                currentExcelFile.outputStream().use { fileOutputStream ->
                                    workbook.write(fileOutputStream)
                                }
                                workbook.close()

                                // 直接流式添加到ZIP，无需将整个ZIP读入内存
                                val zipEntry = ZipEntry("rule_results_${ruleId}_part${fileIndex}.xlsx")
                                zipStream.putNextEntry(zipEntry)
                                FileInputStream(currentExcelFile).use { fileInputStream ->
                                    fileInputStream.copyTo(zipStream)
                                }
                                zipStream.closeEntry()
                                zipStream.flush() // 立即刷新到客户端

                                log.info("第 {} 个Excel文件流式处理完成，包含 {} 条记录", fileIndex, currentFileProcessedCount)

                            } finally {
                                // 立即清理当前Excel临时文件
                                if (currentExcelFile.exists()) {
                                    currentExcelFile.delete()
                                }
                            }
                        }
                    }

                    val elapsedTime = System.currentTimeMillis() - startTime
                    log.info("exportMultipleExcelFilesAsZipStreaming | 完成 | 流式导出ZIP成功 | ruleId: {} | fileCount: {} | totalCount: {} | 耗时: {}ms", ruleId, fileCount, totalCount, elapsedTime)

                } catch (e: Exception) {
                    val elapsedTime = System.currentTimeMillis() - startTime
                    log.error("exportMultipleExcelFilesAsZipStreaming | 异常 | 流式导出ZIP失败 | ruleId: {} | 耗时: {}ms | 异常: {}", ruleId, elapsedTime, e.message, e)
                    throw e
                }
            }

            // 设置响应头
            val headers = HttpHeaders()
            headers.contentType = MediaType.parseMediaType("application/zip")
            headers.setContentDispositionFormData("attachment", "rule_results_${ruleId}_${System.currentTimeMillis()}.zip")
            headers.set("Transfer-Encoding", "chunked")

            return ResponseEntity.ok()
                .headers(headers)
                .body(streamingResponseBody)

        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error("exportMultipleExcelFilesAsZipStreaming | 异常 | 流式导出ZIP初始化失败 | ruleId: {} | 耗时: {}ms | 异常: {}", ruleId, elapsedTime, e.message, e)
            throw e
        }
    }

    private fun fillExcelRow(row: org.apache.poi.ss.usermodel.Row, result: RuleResultEntity, baseHeaders: List<String>, sortedJsonKeys: List<String>, dateFormatter: DateTimeFormatter) {
        // 解析JSON数据
        val jsonNode = try {
            objectMapper.readTree(result.rowData)
        } catch (e: Exception) {
            log.warn("JSON解析失败 | rowData: {} | 异常: {}", result.rowData, e.message)
            null
        }

        // 填充基础列
        row.createCell(0).setCellValue(result.id?.toString() ?: "")
        row.createCell(1).setCellValue(result.ruleId.toString())
        row.createCell(2).setCellValue(result.dbType)
        row.createCell(3).setCellValue(result.executedAt.format(dateFormatter))
        row.createCell(4).setCellValue(if (result.isFixed == 1) "已修复" else "未修复")
        row.createCell(5).setCellValue(result.assignmentStatus)
        
        // Handle software dev req number list - display as comma-separated string
        val softwareDevReqNumberDisplay = try {
            if (!result.softwareDevReqNumberList.isNullOrBlank()) {
                val list = objectMapper.readValue(result.softwareDevReqNumberList, Array<String>::class.java)
                list.joinToString(", ")
            } else {
                ""
            }
        } catch (e: Exception) {
            result.softwareDevReqNumberList ?: ""
        }
        row.createCell(6).setCellValue(softwareDevReqNumberDisplay)
        row.createCell(7).setCellValue(result.issueCauseType ?: "")
        row.createCell(8).setCellValue(result.issueCauseDetail ?: "")

        // 填充JSON展开列
        sortedJsonKeys.forEachIndexed { jsonIndex, jsonKey ->
            val columnIndex = baseHeaders.size + jsonIndex
            val cellValue = if (jsonNode?.isObject == true && jsonNode.has(jsonKey)) {
                val fieldValue = jsonNode.get(jsonKey)
                when {
                    fieldValue.isTextual -> fieldValue.asText()
                    fieldValue.isNumber -> fieldValue.asText()
                    fieldValue.isBoolean -> fieldValue.asBoolean().toString()
                    fieldValue.isNull -> ""
                    else -> fieldValue.toString()
                }
            } else {
                ""
            }
            row.createCell(columnIndex).setCellValue(cellValue)
        }
    }

    private fun exportResultsByRuleIdAsCsv(ruleId: Long): ResponseEntity<ByteArray> {
        log.info("e30a7a8c | exportResultsByRuleIdAsCsv | 开始 | 导出规则执行结果到CSV | ruleId: {}", ruleId)
        val startTime = System.currentTimeMillis()

        try {
            // 查找最新的规则执行结果，获取runId
            log.debug("2e84b57b | exportResultsByRuleIdAsCsv | 查找最新的规则执行结果 | ruleId: {}", ruleId)
            val latestRuleResult = ruleResultRepository.findTopByRuleIdOrderByCreatedAtDesc(ruleId)
            val runId = latestRuleResult?.runId

            // 获取总记录数
            val totalCount = if (runId.isNullOrBlank()) {
                ruleResultRepositoryImpl.countByRuleIdForBatch(ruleId)
            } else {
                ruleResultRepositoryImpl.countByRuleIdAndRunIdForBatch(ruleId, runId)
            }

            log.debug("8a44a254 | exportResultsByRuleIdAsCsv | 总记录数: {} | runId: {}", totalCount, runId)

            // 如果没有数据，返回空CSV
            if (totalCount == 0L) {
                log.warn("d6ccbb55 | exportResultsByRuleIdAsCsv | 没有找到数据 | ruleId: {} | runId: {}", ruleId, runId)
                return ResponseEntity.noContent().build()
            }

            // 第一步：收集JSON字段名 - 使用小批量扫描前1000条记录
            val jsonKeys = mutableSetOf<String>()
            val sampleSize = minOf(1000, totalCount.toInt())
            val sampleResults = if (runId.isNullOrBlank()) {
                ruleResultRepositoryImpl.findAllByRuleIdOrderByUpdatedAtDescCursor(ruleId, sampleSize, null, null)
            } else {
                ruleResultRepositoryImpl.findByRuleIdAndRunIdOrderByUpdatedAtDescCursor(
                    ruleId,
                    runId,
                    sampleSize,
                    null,
                    null
                )
            }

            // 解析样本数据收集JSON字段
            sampleResults.forEach { result ->
                try {
                    val jsonNode = objectMapper.readTree(result.rowData)
                    if (jsonNode.isObject) {
                        jsonNode.fieldNames().forEach { fieldName ->
                            jsonKeys.add(fieldName)
                        }
                    }
                } catch (e: Exception) {
                    log.warn(
                        "7cf97cf1 | exportResultsByRuleIdAsCsv | JSON解析失败 | rowData: {} | 异常: {}",
                        result.rowData,
                        e.message
                    )
                }
            }

            // 创建CSV内容
            val csvBuilder = StringBuilder()
            val baseHeaders = listOf("ID", "规则ID", "数据库类型", "执行时间", "是否修复", "分配状态", "软开需求编号列表", "问题原因类型", "问题原因详述")
            val sortedJsonKeys = jsonKeys.sorted() // 按字母顺序排序JSON字段
            val columnHeaders = baseHeaders + sortedJsonKeys

            // 写入CSV头部
            csvBuilder.append(columnHeaders.joinToString(",") { escapeCSVField(it) }).append("\n")

            // 批量处理数据 - 使用cursor-based pagination
            val batchSize = 20000 // 每批处理20000条记录，减少数据库查询次数
            val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            var processedCount = 0
            var lastUpdatedAt: LocalDateTime? = null
            var lastId: Long? = null

            while (processedCount < totalCount) {
                log.info(
                    "f1g2h3i4 | exportResultsByRuleIdAsCsv | 处理批次 | processedCount: {} | batchSize: {} | lastUpdatedAt: {} | lastId: {}",
                    processedCount, batchSize, lastUpdatedAt, lastId
                )

                // 批量查询数据 - 使用cursor-based pagination with composite cursor
                val batchQueryStart = System.currentTimeMillis()
                val batchResults = if (runId.isNullOrBlank()) {
                    ruleResultRepositoryImpl.findAllByRuleIdOrderByUpdatedAtDescCursor(
                        ruleId,
                        batchSize,
                        lastUpdatedAt,
                        lastId
                    )
                } else {
                    ruleResultRepositoryImpl.findByRuleIdAndRunIdOrderByUpdatedAtDescCursor(
                        ruleId,
                        runId,
                        batchSize,
                        lastUpdatedAt,
                        lastId
                    )
                }
                val batchQueryTime = System.currentTimeMillis() - batchQueryStart
                log.info(
                    "f1g2h3i4 | exportResultsByRuleIdAsCsv | 批次查询完成 | 返回记录数: {} | 查询耗时: {}ms",
                    batchResults.size, batchQueryTime
                )

                // 如果没有更多数据，退出循环
                if (batchResults.isEmpty()) {
                    break
                }

                // 处理当前批次的数据
                val batchProcessStart = System.currentTimeMillis()
                for (result in batchResults) {
                    val row = mutableListOf<String>()

                    // 解析JSON数据
                    val jsonNode = try {
                        objectMapper.readTree(result.rowData)
                    } catch (e: Exception) {
                        log.warn(
                            "f1g2h3i4 | exportResultsByRuleIdAsCsv | JSON解析失败 | rowData: {} | 异常: {}",
                            result.rowData,
                            e.message
                        )
                        null
                    }

                    // 填充基础列
                    row.add(result.id?.toString() ?: "")
                    row.add(result.ruleId.toString())
                    row.add(result.dbType)
                    row.add(result.executedAt.format(dateFormatter))
                    row.add(if (result.isFixed == 1) "已修复" else "未修复")
                    row.add(result.assignmentStatus)
                    
                    // Handle software dev req number list - display as comma-separated string
                    val softwareDevReqNumberDisplay = try {
                        if (!result.softwareDevReqNumberList.isNullOrBlank()) {
                            val list = objectMapper.readValue(result.softwareDevReqNumberList, Array<String>::class.java)
                            list.joinToString(", ")
                        } else {
                            ""
                        }
                    } catch (e: Exception) {
                        result.softwareDevReqNumberList ?: ""
                    }
                    row.add(softwareDevReqNumberDisplay)
                    row.add(result.issueCauseType ?: "")
                    row.add(result.issueCauseDetail ?: "")

                    // 填充JSON展开列
                    sortedJsonKeys.forEach { jsonKey ->
                        val cellValue = if (jsonNode?.isObject == true && jsonNode.has(jsonKey)) {
                            val fieldValue = jsonNode.get(jsonKey)
                            when {
                                fieldValue.isTextual -> fieldValue.asText()
                                fieldValue.isNumber -> fieldValue.asText()
                                fieldValue.isBoolean -> fieldValue.asBoolean().toString()
                                fieldValue.isNull -> ""
                                else -> fieldValue.toString()
                            }
                        } else {
                            ""
                        }
                        row.add(cellValue)
                    }

                    // 写入CSV行
                    csvBuilder.append(row.joinToString(",") { escapeCSVField(it) }).append("\n")
                }
                val batchProcessTime = System.currentTimeMillis() - batchProcessStart

                processedCount += batchResults.size

                // 更新cursor - 使用最后一条记录的updated_at和id作为下一次查询的composite cursor
                val lastRecord = batchResults.lastOrNull()
                lastUpdatedAt = lastRecord?.updatedAt
                lastId = lastRecord?.id

                // 记录批次完成和总进度
                log.info(
                    "f1g2h3i4 | exportResultsByRuleIdAsCsv | 批次处理完成 | 本批次记录数: {} | 处理耗时: {}ms | 总进度: {} / {}",
                    batchResults.size, batchProcessTime, processedCount, totalCount
                )

                // 记录进度
                if (processedCount % 20000 == 0) {
                    log.info(
                        "f1g2h3i4 | exportResultsByRuleIdAsCsv | 处理进度里程碑 | 已处理: {} / {} 条记录",
                        processedCount,
                        totalCount
                    )
                }
            }

            // 转换为字节数组
            val csvData = csvBuilder.toString().toByteArray(Charsets.UTF_8)

            val elapsedTime = System.currentTimeMillis() - startTime
            log.info(
                "f1g2h3i4 | exportResultsByRuleIdAsCsv | 完成 | 导出规则执行结果到CSV成功 | ruleId: {} | runId: {} | count: {} | 耗时: {}ms",
                ruleId, runId, processedCount, elapsedTime
            )

            // 设置响应头
            val headers = HttpHeaders()
            headers.contentType = MediaType.parseMediaType("text/csv; charset=utf-8")
            headers.setContentDispositionFormData(
                "attachment",
                "rule_results_${ruleId}_${System.currentTimeMillis()}.csv"
            )

            return ResponseEntity.ok()
                .headers(headers)
                .body(csvData)

        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error(
                "f1g2h3i4 | exportResultsByRuleIdAsCsv | 异常 | 导出规则执行结果到CSV失败 | ruleId: {} | 耗时: {}ms | 异常: {}",
                ruleId, elapsedTime, e.message, e
            )
            throw e
        }
    }

    private fun exportResultsByRuleIdAsCsvStreaming(ruleId: Long): ResponseEntity<StreamingResponseBody> {
        log.info(
            "f1g2h3i4 | exportResultsByRuleIdAsCsvStreaming | 开始 | 导出规则执行结果到CSV(流式) | ruleId: {}",
            ruleId
        )
        val startTime = System.currentTimeMillis()

        try {
            // 查找最新的规则执行结果，获取runId
            val latestRuleResult = ruleResultRepository.findTopByRuleIdOrderByCreatedAtDesc(ruleId)
            val runId = latestRuleResult?.runId

            // 获取总记录数
            val totalCount = if (runId.isNullOrBlank()) {
                ruleResultRepositoryImpl.countByRuleIdForBatch(ruleId)
            } else {
                ruleResultRepositoryImpl.countByRuleIdAndRunIdForBatch(ruleId, runId)
            }

            log.info("f1g2h3i4 | exportResultsByRuleIdAsCsvStreaming | 总记录数: {} | runId: {}", totalCount, runId)

            if (totalCount == 0L) {
                log.warn(
                    "f1g2h3i4 | exportResultsByRuleIdAsCsvStreaming | 没有找到数据 | ruleId: {} | runId: {}",
                    ruleId,
                    runId
                )
                return ResponseEntity.noContent().build()
            }

            // 收集JSON字段名
            val jsonKeys = mutableSetOf<String>()
            val sampleSize = minOf(10, totalCount.toInt())
            val sampleResults = if (runId.isNullOrBlank()) {
                ruleResultRepositoryImpl.findAllByRuleIdOrderByUpdatedAtDescCursor(ruleId, sampleSize, null, null)
            } else {
                ruleResultRepositoryImpl.findByRuleIdAndRunIdOrderByUpdatedAtDescCursor(
                    ruleId,
                    runId,
                    sampleSize,
                    null,
                    null
                )
            }

            sampleResults.forEach { result ->
                try {
                    val jsonNode = objectMapper.readTree(result.rowData)
                    if (jsonNode.isObject) {
                        jsonNode.fieldNames().forEach { fieldName ->
                            jsonKeys.add(fieldName)
                        }
                    }
                } catch (e: Exception) {
                    log.warn(
                        "7cf97cf1 | exportResultsByRuleIdAsCsvStreaming | JSON解析失败 | rowData: {} | 异常: {}",
                        result.rowData,
                        e.message
                    )
                }
            }

            val baseHeaders = listOf("ID", "规则ID", "数据库类型", "执行时间", "是否修复", "分配状态", "软开需求编号列表", "问题原因类型", "问题原因详述")
            val sortedJsonKeys = jsonKeys.sorted()
            val columnHeaders = baseHeaders + sortedJsonKeys

            // 创建流式响应
            val streamingResponseBody = StreamingResponseBody { outputStream ->
                try {
                    val writer = outputStream.bufferedWriter(Charsets.UTF_8)

                    // 写入CSV头部
                    writer.write(columnHeaders.joinToString(",") { escapeCSVField(it) })
                    writer.write("\n")
                    writer.flush()

                    // 流式处理数据
                    val batchSize = 20000
                    val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                    var processedCount = 0
                    var lastUpdatedAt: LocalDateTime? = null
                    var lastId: Long? = null

                    while (processedCount < totalCount) {
                        log.info(
                            "f1g2h3i4 | exportResultsByRuleIdAsCsvStreaming | 流式处理批次 | processedCount: {} | batchSize: {}",
                            processedCount, batchSize
                        )

                        val batchQueryStart = System.currentTimeMillis()
                        val batchResults = if (runId.isNullOrBlank()) {
                            ruleResultRepositoryImpl.findAllByRuleIdOrderByUpdatedAtDescCursor(
                                ruleId,
                                batchSize,
                                lastUpdatedAt,
                                lastId
                            )
                        } else {
                            ruleResultRepositoryImpl.findByRuleIdAndRunIdOrderByUpdatedAtDescCursor(
                                ruleId,
                                runId,
                                batchSize,
                                lastUpdatedAt,
                                lastId
                            )
                        }
                        val batchQueryTime = System.currentTimeMillis() - batchQueryStart
                        log.info(
                            "f1g2h3i4 | exportResultsByRuleIdAsCsvStreaming | 批次查询完成 | 返回记录数: {} | 查询耗时: {}ms",
                            batchResults.size, batchQueryTime
                        )

                        if (batchResults.isEmpty()) {
                            break
                        }

                        // 流式写入CSV数据
                        val batchProcessStart = System.currentTimeMillis()
                        for (result in batchResults) {
                            val row = mutableListOf<String>()

                            val jsonNode = try {
                                objectMapper.readTree(result.rowData)
                            } catch (e: Exception) {
                                log.warn(
                                    "f1g2h3i4 | exportResultsByRuleIdAsCsvStreaming | JSON解析失败 | rowData: {} | 异常: {}",
                                    result.rowData,
                                    e.message
                                )
                                null
                            }

                            // 填充基础列
                            row.add(result.id?.toString() ?: "")
                            row.add(result.ruleId.toString())
                            row.add(result.dbType)
                            row.add(result.executedAt.format(dateFormatter))
                            row.add(if (result.isFixed == 1) "已修复" else "未修复")
                            row.add(result.assignmentStatus)
                            
                            // Handle software dev req number list - display as comma-separated string
                            val softwareDevReqNumberDisplay = try {
                                if (!result.softwareDevReqNumberList.isNullOrBlank()) {
                                    val list = objectMapper.readValue(result.softwareDevReqNumberList, Array<String>::class.java)
                                    list.joinToString(", ")
                                } else {
                                    ""
                                }
                            } catch (e: Exception) {
                                result.softwareDevReqNumberList ?: ""
                            }
                            row.add(softwareDevReqNumberDisplay)
                            row.add(result.issueCauseType ?: "")
                            row.add(result.issueCauseDetail ?: "")

                            // 填充JSON展开列
                            sortedJsonKeys.forEach { jsonKey ->
                                val cellValue = if (jsonNode?.isObject == true && jsonNode.has(jsonKey)) {
                                    val fieldValue = jsonNode.get(jsonKey)
                                    when {
                                        fieldValue.isTextual -> fieldValue.asText()
                                        fieldValue.isNumber -> fieldValue.asText()
                                        fieldValue.isBoolean -> fieldValue.asBoolean().toString()
                                        fieldValue.isNull -> ""
                                        else -> fieldValue.toString()
                                    }
                                } else {
                                    ""
                                }
                                row.add(cellValue)
                            }

                            // 写入CSV行并立即刷新
                            writer.write(row.joinToString(",") { escapeCSVField(it) })
                            writer.write("\n")
                        }
                        writer.flush() // 每批次后立即刷新到客户端
                        val batchProcessTime = System.currentTimeMillis() - batchProcessStart

                        processedCount += batchResults.size

                        // 更新cursor
                        val lastRecord = batchResults.lastOrNull()
                        lastUpdatedAt = lastRecord?.updatedAt
                        lastId = lastRecord?.id

                        log.info(
                            "f1g2h3i4 | exportResultsByRuleIdAsCsvStreaming | 批次流式写入完成 | 本批次记录数: {} | 处理耗时: {}ms | 总进度: {} / {}",
                            batchResults.size, batchProcessTime, processedCount, totalCount
                        )
                    }

                    writer.flush()
                    writer.close()

                    val elapsedTime = System.currentTimeMillis() - startTime
                    log.info(
                        "f1g2h3i4 | exportResultsByRuleIdAsCsvStreaming | 完成 | 流式导出CSV成功 | ruleId: {} | runId: {} | count: {} | 耗时: {}ms",
                        ruleId, runId, processedCount, elapsedTime
                    )

                } catch (e: Exception) {
                    val elapsedTime = System.currentTimeMillis() - startTime
                    log.error(
                        "f1g2h3i4 | exportResultsByRuleIdAsCsvStreaming | 异常 | 流式导出CSV失败 | ruleId: {} | 耗时: {}ms | 异常: {}",
                        ruleId, elapsedTime, e.message, e
                    )
                    throw e
                }
            }

            // 设置响应头
            val headers = HttpHeaders()
            headers.contentType = MediaType.parseMediaType("text/csv; charset=utf-8")
            headers.setContentDispositionFormData(
                "attachment",
                "rule_results_${ruleId}_${System.currentTimeMillis()}.csv"
            )
            headers.set("Transfer-Encoding", "chunked")

            return ResponseEntity.ok()
                .headers(headers)
                .body(streamingResponseBody)

        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error(
                "f1g2h3i4 | exportResultsByRuleIdAsCsvStreaming | 异常 | 流式导出CSV失败 | ruleId: {} | 耗时: {}ms | 异常: {}",
                ruleId, elapsedTime, e.message, e
            )
            throw e
        }
    }

    private fun escapeCSVField(field: String): String {
        // 如果字段包含逗号、双引号或换行符，需要用双引号包围，并转义内部的双引号
        return if (field.contains(",") || field.contains("\"") || field.contains("\n") || field.contains("\r")) {
            "\"${field.replace("\"", "\"\"")}\""
        } else {
            field
        }
    }
}
