-- Combined DDL Migration for 2025-08-21
-- Description: Database schema changes including new columns and performance optimization indexes
-- MySQL 5.7 compatible

-- =============================================================================
-- TABLE STRUCTURE MODIFICATIONS
-- =============================================================================

-- Add new columns to rule_execution_results table
ALTER TABLE rule_execution_results 
ADD COLUMN SOFTWARE_DEV_REQ_NUMBER_LIST JSON NULL COMMENT '软开需求编号列表' AFTER IS_FIXED,
ADD COLUMN ASSIGNMENT_STATUS varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '未分配' COMMENT '分配状态: 已分配/未分配' AFTER SOFTWARE_DEV_REQ_NUMBER_LIST,
ADD COLUMN ISSUE_CAUSE_TYPE varchar(255) COLLATE utf8mb4_unicode_ci NULL COMMENT '问题原因类型' AFTER ASSIGNMENT_STATUS,
ADD COLUMN ISSUE_CAUSE_DETAIL text COLLATE utf8mb4_unicode_ci NULL COMMENT '问题原因详述' AFTER ISSUE_CAUSE_TYPE;

-- =============================================================================
-- PERFORMANCE OPTIMIZATION INDEXES
-- =============================================================================

-- Indexes for new columns in rule_execution_results
CREATE INDEX idx_assignment_status ON rule_execution_results(ASSIGNMENT_STATUS);
CREATE INDEX idx_issue_cause_type ON rule_execution_results(ISSUE_CAUSE_TYPE);

-- Critical missing foreign key indexes for validate_task_rule table
CREATE INDEX idx_vtr_task_id ON validate_task_rule(TASK_ID);
CREATE INDEX idx_vtr_rule_id ON validate_task_rule(RULE_ID);

-- Search filter indexes for data_quality_issues
CREATE INDEX idx_dqi_status ON data_quality_issues(STATUS);
CREATE INDEX idx_dqi_assigned_to ON data_quality_issues(ASSIGNED_TO);

-- ORDER BY columns for performance
CREATE INDEX idx_dqi_create_at ON data_quality_issues(CREATE_AT DESC);
CREATE INDEX idx_dqi_update_at ON data_quality_issues(UPDATE_AT DESC);

-- Composite index for expensive correlated subquery optimization
CREATE INDEX idx_dqi_rule_update ON data_quality_issues(RULE_ID, UPDATE_AT DESC);

-- Task name search index for validate_task table
CREATE INDEX idx_vt_task_name ON validate_task(TASK_NAME);