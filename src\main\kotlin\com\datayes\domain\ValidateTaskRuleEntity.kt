package com.datayes.domain

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table

import java.time.LocalDateTime

@Table("validate_task_rule")
data class ValidateTaskRuleEntity(

    @Id
    val id: Long? = null,

    @Column("TASK_ID")
    val taskId: Long,

    @Column("RULE_ID")
    val ruleId: Long? = null,

    @Column("RULE_NAME")
    val ruleName: String? = null,

    @Column("SYSTEM_NAME")
    val systemName: String? = null,

    @Column("RULE_CATEGORY")
    val ruleCategory: String? = null,

    @Column("BUSINESS_TYPE")
    val businessType: String? = null,

    @Column("STATUS")
    val status: String? = null,

    @Column("QA_ACTIVE_FLG")
    val qaActiveFlg: Boolean = true,

    @Column("CREATE_BY")
    val createBy: String? = null,

    @Column("CREATE_TIME")
    val createTime: LocalDateTime? = null,

    @Column("UPDATE_BY")
    val updateBy: String? = null,

    @Column("UPDATE_TIME")
    val updateTime: LocalDateTime? = null,
)
