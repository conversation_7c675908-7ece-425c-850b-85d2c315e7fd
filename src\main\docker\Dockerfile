FROM registry.minshenglife.com/infrastructure/openjdk:17.0.2-slim-bullseyearm

# 设置时区为 Asia/Shanghai（假设基础镜像已包含必要的时区数据）
ENV TZ=Asia/Shanghai

# 设置工作目录
WORKDIR /app

# 添加应用JAR包
ADD target/dgp-issue-management-0.0.1-SNAPSHOT.jar /app/dgp-issue-management-0.0.1-SNAPSHOT.jar

# 暴露应用端口
EXPOSE 9500

# 设置容器启动命令
ENTRYPOINT ["java", "-XX:+UseG1GC", "-Xmx1536m", "-Xms512m", "-XX:+ExplicitGCInvokesConcurrent", "-XX:+ParallelRefProcEnabled", "-XX:MaxGCPauseMillis=200", "-XX:+DisableExplicitGC", "-XX:+HeapDumpOnOutOfMemoryError", "-XX:HeapDumpPath=/app/heapdump.hprof", "-Djava.security.egd=file:/dev/./urandom", "-Dorg.apache.poi.ss.ignoreMissingFontSystem=true", "-Djava.awt.headless=true", "-jar", "/app/dgp-issue-management-0.0.1-SNAPSHOT.jar"]
