package com.datayes.domain

import java.time.LocalDateTime

/**
 * DTO for displaying detailed issue information, merging IssueEntity, TechRuleEntity, BusinessRuleInfoEntity, and OrganizationInfoEntity.
 */
data class IssueWithRule(

    // Fields from IssueEntity
    val issueId: Long?,
    val initialCount: Int?,
    val remainingCount: Int?,
    val assignedTo: String?,
    val issueCurrentStage: IssueStage?,
    val issueCreateBy: String,
    val issueCreatedAt: LocalDateTime,
    val issueUpdateBy: String?,
    val issueUpdatedAt: LocalDateTime?,
    val isFixed: Boolean,

    // Fields from TechRuleEntity
    val ruleId: Long,
    val ruleName: String,
    val systemName: Long?,
    val ruleCategory: Long?,
    val ruleType: Long?,
    val directorBy: String?,
    val ruleStatus: Byte?,
    val ruleDescription: String?,
    val checkType: Byte?,
    val dataSource: String?,
    val databaseName: String?,
    val tableName: String?,
    val ruleCreateBy: String?, // Added from TechRuleEntity
    val ruleCreateTime: LocalDateTime?, // Added from TechRuleEntity
    val ruleUpdateBy: String?, // Added from TechRuleEntity
    val ruleUpdateTime: LocalDateTime?, // Added from TechRuleEntity

    // Fields from BusinessRuleInfoEntity
    val businessRuleId: Long?,
    val businessRuleName: String?,

    // Fields from OrganizationInfoEntity
    val orgCode: Long?,
    val orgName: String?,
)

fun mergeIssueAndRule(
    issue: IssueEntity,
    techRule: TechRuleEntity,
    first: IssueRunHistoryEntity?,
    last: IssueRunHistoryEntity?,
    businessRule: BusinessRuleInfoEntity? = null,
    organization: OrganizationInfoEntity? = null,
): IssueWithRule {
    return IssueWithRule(
        // Mapping fields from IssueEntity
        issueId = issue.id,
        initialCount = first?.errorRowCount,
        remainingCount = last?.errorRowCount,
        assignedTo = issue.assignedTo,
        issueCurrentStage = issue.currentStage,
        issueCreateBy = issue.createBy,
        issueCreatedAt = issue.createdAt,
        issueUpdateBy = issue.updateBy,
        issueUpdatedAt = issue.updatedAt,
        // 统一 isFixed 语义：仅当 COMPLETED 且 latestErrorRowCount == 0 视为 true，其余为 false
        isFixed = (issue.currentStage == IssueStage.COMPLETED) && ((issue.latestErrorRowCount ?: Long.MAX_VALUE) == 0L),

        // Mapping fields from TechRuleEntity
        ruleId = techRule.id ?: issue.ruleId, // Use ruleId from issue as fallback if techRule.id is null
        ruleName = techRule.ruleName,
        systemName = techRule.systemName,
        ruleCategory = techRule.ruleCategory,
        ruleType = techRule.ruleType,
        directorBy = techRule.directorBy,
        ruleStatus = techRule.ruleStatus,
        ruleDescription = techRule.ruleDescription,
        checkType = techRule.checkType,
        dataSource = techRule.dataSource,
        databaseName = techRule.databaseName,
        tableName = techRule.tableName,
        ruleCreateBy = techRule.createBy,
        ruleCreateTime = techRule.createTime,
        ruleUpdateBy = techRule.updateBy,
        ruleUpdateTime = techRule.updateTime,

        // Mapping fields from BusinessRuleInfoEntity
        businessRuleId = techRule.businessRuleId,
        businessRuleName = businessRule?.ruleName,

        // Mapping fields from OrganizationInfoEntity
        orgCode = businessRule?.orgCode,
        orgName = organization?.orgName,
    )
}
