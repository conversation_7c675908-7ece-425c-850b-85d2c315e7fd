package com.datayes.spring

import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.slf4j.MDC
import org.springframework.stereotype.Component
import java.util.UUID

/**
 * 请求ID切面，用于生成和传递请求ID
 */
@Aspect
@Component
class RequestIdAspect {

    @Around("@within(org.springframework.web.bind.annotation.RestController) || @within(org.springframework.stereotype.Controller)")
    fun aroundControllerMethods(joinPoint: ProceedingJoinPoint): Any? {
        // 生成请求ID
        val requestId = UUID.randomUUID().toString().replace("-", "").substring(0, 8)
        
        try {
            // 将请求ID放入MDC上下文
            MDC.put("requestId", requestId)
            
            // 执行原方法
            return joinPoint.proceed()
        } finally {
            // 清理MDC上下文
            MDC.remove("requestId")
        }
    }
}