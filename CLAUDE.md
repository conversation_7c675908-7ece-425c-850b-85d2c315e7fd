# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

This is a Maven-based Kotlin Spring Boot project. Use these commands:

**Build and Run:**
- `mvn clean compile` - Compile the project
- `mvn clean package` - Build JAR package
- `mvn spring-boot:run` - Run the application (starts on port 9500)

**Testing:**
- `mvn test` - Run all tests
- `mvn test -Dtest=ClassName` - Run specific test class
- `mvn test -Dtest=ClassName#methodName` - Run specific test method
- **IMPORTANT:** NEVER run tests unless explicitly requested by the user

**Kotlin Code Quality:**
- **NEVER use `!!` (force unwrap) in production code** - Only allowed in test code
- Always use proper null checks with meaningful error messages
- Use Elvis operator `?:` with early returns for validation
- Use `mapNotNull` and safe calls to handle nullable collections
- Enable Kotlin smart cast through proper null assertions

**Database:**
- MySQL database connection configured in `application.properties`
- Database schema located in `docs/ddl.sql`
- SQL migrations in `src/main/resources/sql/`

## Architecture Overview

### Core Domain
This is a data quality issue management system with the following key domain entities:

**Main Entities:**
- `IssueEntity` - Represents data quality issues with lifecycle stages (NOT_STARTED, WAITING, RUNNING, ERROR, COMPLETED, CRASHED)
- `TechRuleEntity` - Technical validation rules that generate issues
- `IssueRunHistoryEntity` - Execution history tracking with run status (STARTED, COMPLETED, FAILED)
- `RuleExecutionLogEntity` - Detailed execution logs for rules
- `RuleResultEntity` - Results of rule executions

### REST API Structure
The application exposes REST endpoints organized by functional areas:

**Main Controllers:**
- `IssueController` (/api/issues) - CRUD operations for issues, search with filtering, run history retrieval
- `RuleManagementController` (/api/rule-management) - Cross-table rule data management and deletion
- `RuleExecutionLogController` - Rule execution logging
- `RuleResultController` - Rule execution results
- `HeartbeatController` - Health check endpoint

### Data Access Layer
Uses Spring Data JDBC with custom repository implementations:
- Repositories follow naming pattern: `EntityNameRepository`
- Complex queries use native SQL for performance
- Supports pagination and filtering via custom query methods

### Cross-Cutting Concerns
- **Logging:** Structured logging with request IDs via `RequestIdAspect`
- **API Documentation:** Swagger/OpenAPI integration via `SwaggerConfig`
- **Error Handling:** Global exception handling via `GlobalExceptionHandler`
- **Response Format:** Standardized response wrapper via `StandardResponse`

### Database Schema
- Primary tables: `data_quality_issues`, `issue_run_history`, `tech_rules`, `rule_execution_log`, `rule_results`
- Uses MySQL with connection pooling
- Supports versioning via @Version annotation

### Key Integration Points
The system manages the complete lifecycle of data quality rules and their generated issues, with detailed tracking of execution history and results across multiple related tables.

## Database Query Tool

Use the Python database query tool for read-only database exploration:

```bash
# List all tables
python db-query-tool.py --list-tables

# Describe table structure
python db-query-tool.py --describe data_quality_issues

# Execute custom read-only queries
python db-query-tool.py --query "SELECT * FROM data_quality_issues LIMIT 10"
```

**IMPORTANT:** 
- Tool is pre-configured, no package installation needed
- NEVER use --interactive mode
- ONLY read operations allowed (SELECT, SHOW, DESCRIBE, EXPLAIN)
- NO modifications to database (INSERT, UPDATE, DELETE, DROP, etc.)