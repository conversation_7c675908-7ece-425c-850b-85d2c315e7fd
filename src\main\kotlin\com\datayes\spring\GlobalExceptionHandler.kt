package com.datayes.spring

import com.datayes.domain.StandardResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler

/**
 * 全局异常处理器
 */
@ControllerAdvice
@Tag(name = "异常处理")
class GlobalExceptionHandler {

    private val logger = LoggerFactory.getLogger(this::class.java)

    /**
     * 处理系统异常
     */
    @ExceptionHandler(Exception::class)
    fun handleException(e: Exception): ResponseEntity<StandardResponse<Unit>> {
        logger.error("系统异常", e)
        return ResponseEntity
            .status(HttpStatus.OK)
            .body(StandardResponse.failure("系统错误: ${e.message}"))
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException::class)
    fun handleIllegalArgumentException(e: IllegalArgumentException): ResponseEntity<StandardResponse<Unit>> {
        logger.warn("非法参数异常", e)
        return ResponseEntity
            .status(HttpStatus.OK)
            .body(StandardResponse.failure("参数错误: ${e.message}"))
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException::class)
    fun handleRuntimeException(e: RuntimeException): ResponseEntity<StandardResponse<Unit>> {
        logger.error("运行时异常", e)
        return ResponseEntity
            .status(HttpStatus.OK)
            .body(StandardResponse.failure("系统错误: ${e.message}"))
    }
}