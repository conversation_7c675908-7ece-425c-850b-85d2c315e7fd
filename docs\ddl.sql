CREATE TABLE `data_quality_issues`
(
    `ID`              bigint                                  NOT NULL AUTO_INCREMENT,
    `RULE_ID`         bigint                                  NOT NULL,
    `INITIAL_COUNT`   int                                              DEFAULT NULL,
    `REMAINING_COUNT` int                                              DEFAULT NULL,
    `STATUS`          varchar(50) COLLATE utf8mb4_unicode_ci           DEFAULT NULL,
    `ASSIGNED_TO`     varchar(255) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `CURRENT_STAGE`   varchar(255) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `CREATE_BY`       varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    `CREATE_AT`       datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UPDATE_BY`       varchar(255) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `UPDATE_AT`       datetime                                         DEFAULT NULL,
    PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
create table rule_execution_results
(
    ID                    bigint auto_increment
        primary key,
    RULE_ID               varchar(64) collate utf8mb4_unicode_ci not null comment '规则ID',
    DB_TYPE               varchar(32) collate utf8mb4_unicode_ci not null comment '数据库类型',
    ORIGINAL_SQL          text collate utf8mb4_unicode_ci        not null comment '原始SQL',
    RUN_ID                varchar(64) collate utf8mb4_unicode_ci not null comment '运行ID',
    EXECUTED_AT           datetime                               not null comment '执行时间',
    ROW_DATA              longtext collate utf8mb4_unicode_ci    not null comment '行数据JSON字符串',
    BUSINESS_KEY_DATA_MD5 varchar(32) collate utf8mb4_unicode_ci null comment 'ROW_DATA的MD5哈希值',
    ROW_INDEX             int                                    not null comment '行索引',
    EXECUTION_TIME_MS     bigint                                 not null comment '执行时间(毫秒)',
    CREATED_BY            varchar(64) collate utf8mb4_unicode_ci null comment '创建人',
    CREATED_AT            datetime                               not null comment '创建时间',
    UPDATED_BY            varchar(64) collate utf8mb4_unicode_ci null comment '更新人',
    UPDATED_AT            datetime                               null comment '更新时间',
    IS_FIXED              tinyint                                null,
    BUSINESS_KEY_DATA     longtext                               not null,
    constraint idx_rule_md5
        unique (RULE_ID, BUSINESS_KEY_DATA_MD5)
) comment '查询结果行表';
