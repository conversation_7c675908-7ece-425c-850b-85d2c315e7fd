package com.datayes.spring

import org.springframework.context.annotation.Configuration
import org.springframework.web.servlet.config.annotation.CorsRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer

@Configuration
class WebConfig : WebMvcConfigurer {

    override fun addCorsMappings(registry: CorsRegistry) {
        registry.addMapping("/**")
            // 使用allowedOriginPatterns替代allowedOrigins("*")
            .allowedOriginPatterns("*")  // 允许所有来源。生产环境建议限制为具体的域名
            .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
            .allowedHeaders("*")
            .allowCredentials(true)
            .maxAge(3600)  // 缓存预检请求结果的时间（秒）
    }
}