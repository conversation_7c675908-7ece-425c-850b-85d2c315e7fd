# Excel Export Performance Improvements

## Current Issue
After implementing SXSSFWorkbook to fix OOM, there are still optimization opportunities and potential issues with the current temp file approach.

## Problems to Address

### 1. Temp File Race Condition
- **Issue**: Temp file deleted in `finally` block before HTTP response fully sent
- **Symptom**: Subsequent export requests may fail after first download completes
- **Root Cause**: `tempFile.readBytes()` loads entire file into memory, defeating SXSSFWorkbook benefits

### 2. Memory Usage Still High
```kotlin
// Current problematic code:
val excelData = tempFile.readBytes() // Loads entire file into memory again!
```
This negates the memory benefits of SXSSFWorkbook.

### 3. Browser Caching Issues
- `window.open()` may cache first request
- Need proper cache-control headers

## Recommended Solutions

### Option 1: Direct Streaming (Preferred)
```kotlin
// Remove temp file entirely, stream directly
val streamingResponseBody = StreamingResponseBody { outputStream ->
    val workbook = SXSSFWorkbook(1000)
    val sheet = workbook.createSheet("规则执行结果")
    
    // Process data and write directly to outputStream
    // ... data processing logic ...
    
    workbook.write(outputStream)
    workbook.close()
}

return ResponseEntity.ok()
    .headers(headers)
    .body(streamingResponseBody)
```

### Option 2: Improved Temp File Handling
```kotlin
// If temp file needed, stream from file instead of loading to memory
return ResponseEntity.ok()
    .headers(headers)
    .body(InputStreamResource(FileInputStream(tempFile)))
    
// Use @PreDestroy or delayed cleanup instead of immediate deletion
```

### Option 3: Add Cache Control Headers
```kotlin
headers.set("Cache-Control", "no-cache, no-store, must-revalidate")
headers.set("Pragma", "no-cache") 
headers.set("Expires", "0")
headers.set("Content-Disposition", "attachment; filename=\"rule_results_${ruleId}_${System.currentTimeMillis()}.xlsx\"")
```

## Implementation Priority
1. **High**: Fix memory issue by implementing direct streaming (Option 1)
2. **Medium**: Add cache control headers (Option 3)
3. **Low**: Alternative temp file approach if streaming doesn't work (Option 2)

## Files to Modify
- `src/main/kotlin/com/datayes/spring/RuleResultController.kt`
- Method: `exportResultsByRuleIdAsExcel()`
- Lines: ~562-600 (temp file handling section)

## Testing Notes
- Test with large datasets (200K+ rows)
- Verify memory usage doesn't spike during export
- Test multiple concurrent downloads
- Test repeat downloads via `window.open()`

## Related Issues
- Original OOM issue at 220K rows (✅ Fixed with SXSSFWorkbook)
- Temp file cleanup timing
- Browser download caching behavior