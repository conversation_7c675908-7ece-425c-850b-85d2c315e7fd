package com.datayes.spring

import com.datayes.domain.RuleResultEntity
import org.springframework.data.repository.CrudRepository
import org.springframework.stereotype.Repository

@Repository
interface RuleResultRepository : CrudRepository<RuleResultEntity, Long> {

    fun findAllByRuleId(ruleId: Long): List<RuleResultEntity>

    // find latest rul id by rule id order by createdAt desc limit 1
    fun findTopByRuleIdOrderByCreatedAtDesc(ruleId: Long): RuleResultEntity?

    fun findByRunId(runId: String): List<RuleResultEntity>

    // find by rule id and run id
    fun findByRuleIdAndRunId(ruleId: Long, runId: String): List<RuleResultEntity>

    // Note: Pagination methods moved to RuleResultRepositoryImpl due to MySQL LIMIT/OFFSET parameter binding issues

    // Count methods for pagination
    fun countByRuleId(ruleId: Long): Long

    fun countByRuleIdAndRunId(ruleId: Long, runId: String): Long
}
