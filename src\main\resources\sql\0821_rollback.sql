-- Rollback Migration for 2025-08-21
-- Description: Rollback all changes made by 0821 DDL and DML migrations
-- MySQL 5.7 compatible
-- IMPORTANT: Execute in reverse order of original migration

-- =============================================================================
-- ROLLBACK DML CHANGES
-- =============================================================================

-- Remove system configuration entry
DELETE FROM system_config WHERE config_key = 'export.excel.max_rows_per_file';

-- =============================================================================
-- ROLLBACK DDL CHANGES
-- =============================================================================

-- Drop performance optimization indexes
DROP INDEX idx_vt_task_name ON validate_task;
DROP INDEX idx_dqi_rule_update ON data_quality_issues;
DROP INDEX idx_dqi_update_at ON data_quality_issues;
DROP INDEX idx_dqi_create_at ON data_quality_issues;
DROP INDEX idx_dqi_assigned_to ON data_quality_issues;
DROP INDEX idx_dqi_status ON data_quality_issues;
DROP INDEX idx_vtr_rule_id ON validate_task_rule;
DROP INDEX idx_vtr_task_id ON validate_task_rule;

-- Drop indexes for new columns in rule_execution_results
DROP INDEX idx_issue_cause_type ON rule_execution_results;
DROP INDEX idx_assignment_status ON rule_execution_results;

-- Remove columns from rule_execution_results table
ALTER TABLE rule_execution_results 
DROP COLUMN ISSUE_CAUSE_DETAIL,
DROP COLUMN ISSUE_CAUSE_TYPE,
DROP COLUMN ASSIGNMENT_STATUS,
DROP COLUMN SOFTWARE_DEV_REQ_NUMBER_LIST;