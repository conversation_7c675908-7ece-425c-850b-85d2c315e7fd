package com.datayes.domain

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "更新问题原因信息请求")
data class UpdateIssueCauseRequest(
    @Schema(description = "规则执行结果ID列表，如果为空则使用ruleId", example = "[1, 2, 3]")
    val ids: List<Long> = emptyList(),
    
    @Schema(description = "规则ID，当ids为空时使用此字段更新该规则下的所有记录", example = "123")
    val ruleId: Long? = null,
    
    @Schema(description = "问题原因类型", example = "数据质量")
    val issueCauseType: String? = null,
    
    @Schema(description = "问题原因详述", example = "字段值不符合规范要求")
    val issueCauseDetail: String? = null,
    
    @Schema(description = "更新人", example = "admin")
    val updatedBy: String? = null
)