package com.datayes.domain

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "查询规则执行结果请求")
data class QueryResultsByRuleIdRequest(
    @Schema(description = "规则ID", required = true, example = "1")
    val ruleId: Long,
    
    @Schema(description = "页码，从1开始", example = "1")
    val page: Int = 1,
    
    @Schema(description = "每页记录数", example = "20")
    val size: Int = 20,
    
    @Schema(description = "JSON字段过滤条件", example = "[{\"key\": \"status\", \"value\": \"active\"}]")
    val filters: List<JsonFilter> = emptyList(),
    
    @Schema(description = "是否修复状态过滤 (0: 未修复, 1: 已修复)", example = "1")
    val isFixed: Int? = null,
    
    @Schema(description = "分配状态过滤", example = "已分配")
    val assignmentStatus: String? = null,
    
    @Schema(description = "软开需求编号 (模糊搜索，搜索JSON列表中的任意值)", example = "REQ-2023-001")
    val softwareDevReqNumber: String? = null,
    
    @Schema(description = "问题原因类型 (精确搜索)", example = "数据质量")
    val issueCauseType: String? = null,
    
    @Schema(description = "问题原因详述 (模糊搜索)", example = "字段值不符合规范")
    val issueCauseDetail: String? = null
)