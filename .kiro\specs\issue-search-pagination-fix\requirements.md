# Requirements Document

## Introduction

The `/api/issues/search` endpoint has a pagination bug where the `totalElements` count doesn't match the actual content size when using the `isFixed` query parameter. This occurs because the database contains multiple issue records for the same rule across different tasks, causing inconsistency between the count query and the content query when grouping by rule ID.

For example, when requesting `page=4, size=10, isFixed=false`, the API returns `totalElements=33` but the content array only contains 3 items instead of the expected 10 (or fewer on the last page).

## Requirements

### Requirement 1

**User Story:** As an API consumer, I want the search results to have consistent pagination where totalElements accurately reflects the number of items that will be returned across all pages, so that I can properly implement pagination controls in the UI.

#### Acceptance Criteria

1. WH<PERSON> I call `/api/issues/search` with pagination parameters THEN the `totalElements` field SHALL accurately represent the total number of unique rule-based issues that match the search criteria
2. WHEN I call `/api/issues/search` with `isFixed` parameter THEN the content array size SHALL be consistent with the pagination logic (size parameter or remaining items on last page)
3. WHEN I navigate through paginated results THEN the sum of all content items across all pages SHALL equal the `totalElements` value

### Requirement 2

**User Story:** As an API consumer, I want the search endpoint to handle rule deduplication consistently between count and content queries, so that pagination works correctly regardless of how many tasks are associated with each rule.

#### Acceptance Criteria

1. WHEN multiple issue records exist for the same rule across different tasks THEN the search SHALL return only one issue record per rule (the most recent one based on UPDATE_AT)
2. WHEN counting total results THEN the count query SHALL use the same deduplication logic as the content query
3. WHEN applying the `isFixed` filter THEN both count and content queries SHALL apply the filter consistently after deduplication

### Requirement 3

**User Story:** As a system administrator, I want the fix to maintain backward compatibility with existing API consumers, so that no breaking changes are introduced to the API contract.

#### Acceptance Criteria

1. WHEN the fix is deployed THEN the API response structure SHALL remain unchanged
2. WHEN existing query parameters are used THEN they SHALL continue to work as expected
3. WHEN no pagination parameters are provided THEN the default behavior SHALL remain consistent with current expectations