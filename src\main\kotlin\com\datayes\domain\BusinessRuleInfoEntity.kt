package com.datayes.domain

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime

@Table("business_rule_info")
data class BusinessRuleInfoEntity(
    @Id
    val id: Long? = null,

    @Column("RULE_NAME")
    val ruleName: String,

    @Column("ORG_CODE")
    val orgCode: Long? = null,

    @Column("CREATE_BY")
    val createBy: String? = null,

    @Column("CREATE_TIME")
    val createTime: LocalDateTime? = null,

    @Column("UPDATE_BY")
    val updateBy: String? = null,

    @Column("UPDATE_TIME")
    val updateTime: LocalDateTime? = null,
)