# Design Document

## Overview

The pagination bug in the `/api/issues/search` endpoint stems from inconsistent deduplication logic between the content query and count query. The current implementation uses different approaches:

- **Content Query**: Uses `searchIssuesGroupedByRuleId()` which properly deduplicates by selecting the most recent issue per rule
- **Count Query**: Uses `searchIssuesCountGroupByRule()` which should count deduplicated results but has inconsistent logic

The fix involves ensuring both queries use identical deduplication and filtering logic.

## Architecture

The solution maintains the existing repository pattern and controller structure while fixing the SQL query inconsistencies:

```
Controller Layer (IssueController)
    ↓
Repository Layer (IssueRepository) 
    ↓ 
Database Layer (MySQL queries)
```

## Root Cause Analysis

Based on database analysis, the issue occurs because:

1. **Multiple Issues per Rule**: The same rule can be associated with multiple tasks, creating multiple issue records
2. **Inconsistent Deduplication**: The count query and content query handle deduplication differently
3. **Filter Application Order**: The `isFixed` filter may be applied before or after deduplication, causing mismatches

Example from database:
- Rule ID 1 has 2 issue records (tasks 1 and 3)
- Rule ID 10 has 2 issue records (tasks 8 and 9)
- When filtering by `isFixed=false`, both records might match, but only one should be counted after deduplication

## Components and Interfaces

### IssueRepository Interface
The repository interface remains unchanged to maintain backward compatibility.

### SQL Query Modifications
Two main queries need alignment:

1. **searchIssuesGroupedByRuleId()** - Content query (already correct)
2. **searchIssuesCountGroupByRule()** - Count query (needs fixing)

## Data Models

No changes to existing data models are required. The fix operates at the SQL query level.

## Error Handling

The existing error handling in the controller remains sufficient. The fix doesn't introduce new error conditions.

## Testing Strategy

### Unit Tests
- Test pagination consistency with various `isFixed` values
- Test edge cases with rules having multiple task associations
- Verify count matches content across multiple pages

### Integration Tests
- Test actual database queries with real data scenarios
- Verify pagination works correctly with different page sizes
- Test boundary conditions (first page, last page, empty results)

### Database Query Testing
- Compare count query results with content query results
- Verify deduplication logic produces identical rule sets
- Test with various filter combinations

## Implementation Approach

### Phase 1: Query Analysis and Alignment
1. Analyze the exact differences between count and content queries
2. Identify the specific SQL logic causing the mismatch
3. Align the count query to match the content query's deduplication logic

### Phase 2: SQL Query Fixes
1. Update `searchIssuesCountGroupByRule()` to use identical WHERE conditions and subquery logic as `searchIssuesGroupedByRuleId()`
2. Update `searchIssuesCountGroupByRuleWithTaskId()` to match `searchIssuesGroupedByRuleIdWithTaskId()`
3. Ensure both queries handle the `isFixed` filter consistently after deduplication

### Phase 3: Validation and Testing
1. Create comprehensive tests to verify pagination consistency
2. Test with various combinations of filters and pagination parameters
3. Validate that the fix doesn't break existing functionality

## Key Design Decisions

### Decision 1: Fix at SQL Level
**Rationale**: The issue is in the SQL queries, not the business logic. Fixing at this level maintains the existing architecture and minimizes risk.

### Decision 2: Maintain Existing API Contract
**Rationale**: Changing the API response structure would be a breaking change. The fix should be transparent to API consumers.

### Decision 3: Use Most Recent Issue per Rule
**Rationale**: The existing content query already uses this logic (MAX(UPDATE_AT)), so the count query should match this approach.

## Performance Considerations

The fix should not impact performance significantly since:
- The same subquery logic is already used in content queries
- Database indexes on RULE_ID and UPDATE_AT should support efficient execution
- The change only affects the count query structure, not the overall query complexity