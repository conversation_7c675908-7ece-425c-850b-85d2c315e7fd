<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <property name="LOG_PATTERN" value="%d{HH:mm:ss.SSS} [%thread] [%X{requestId}] %-5level %logger{36} [%file:%line] - %msg%n"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <logger name="com.datayes" level="INFO" additive="false"/>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>