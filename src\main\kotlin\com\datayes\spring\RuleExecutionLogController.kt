package com.datayes.spring

import com.datayes.domain.RuleExecutionLogEntity
import com.datayes.domain.StandardResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/rule-execution-logs")
@Tag(name = "规则执行日志管理", description = "规则执行日志的查询操作")
class RuleExecutionLogController(
    private val ruleExecutionLogRepository: RuleExecutionLogRepository
) {

    private val log = LoggerFactory.getLogger(RuleExecutionLogController::class.java)

    @GetMapping
    @Operation(
        summary = "根据规则ID和可选的运行ID获取规则执行日志",
        description = "如果提供了运行ID，则返回该运行ID和规则ID对应的所有日志。如果未提供运行ID，则返回该规则ID最新一次运行的所有日志。"
    )
    @ApiResponse(responseCode = "200", description = "成功获取规则执行日志列表")
    fun getRuleExecutionLogs(
        @Parameter(description = "规则ID", required = true)
        @RequestParam("rule_id") ruleId: Long,
        @Parameter(description = "运行ID (可选)。如果未提供，将使用该规则ID的最新运行ID。")
        @RequestParam("run_id", required = false) runId: String?
    ): StandardResponse<List<RuleExecutionLogEntity>> {
        log.info("c8d9e0f1 | getRuleExecutionLogs | 开始 | 查询规则执行日志 | ruleId: {} | runId: {}", ruleId, runId)
        val startTime = System.currentTimeMillis()

        try {
            val actualRunId = if (runId != null) {
                log.debug("c8d9e0f1 | getRuleExecutionLogs | 使用提供的runId | ruleId: {} | runId: {}", ruleId, runId)
                runId
            } else {
                log.debug("c8d9e0f1 | getRuleExecutionLogs | 查找最新的runId | ruleId: {}", ruleId)
                findLatestRunIdForRule(ruleId).also {
                    log.debug("c8d9e0f1 | getRuleExecutionLogs | 找到最新的runId | ruleId: {} | latestRunId: {}", ruleId, it)
                }
            }

            val elapsedTime = System.currentTimeMillis() - startTime

            return if (actualRunId != null) {
                val logs = ruleExecutionLogRepository.findByRuleIdAndRunId(ruleId, actualRunId)
                log.info("c8d9e0f1 | getRuleExecutionLogs | 完成 | 查询规则执行日志成功 | ruleId: {} | runId: {} | count: {} | 耗时: {}ms", 
                    ruleId, actualRunId, logs.size, elapsedTime)
                StandardResponse.success(payload = logs)
            } else {
                // 如果找不到最新的 runId (即该 ruleId 没有任何日志记录)
                log.warn("c8d9e0f1 | getRuleExecutionLogs | 完成 | 未找到规则的任何执行日志 | ruleId: {} | 耗时: {}ms", ruleId, elapsedTime)
                StandardResponse.success(message = "未找到规则ID $ruleId 的任何执行日志", payload = emptyList())
            }
        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error("c8d9e0f1 | getRuleExecutionLogs | 异常 | 查询规则执行日志失败 | ruleId: {} | runId: {} | 耗时: {}ms | 异常: {}", 
                ruleId, runId, elapsedTime, e.message, e)
            throw e
        }
    }

    private fun findLatestRunIdForRule(ruleId: Long): String? {
        log.debug("c8d9e0f1 | findLatestRunIdForRule | 开始 | 查找规则最新的runId | ruleId: {}", ruleId)
        val startTime = System.currentTimeMillis()

        try {
            // 查找该 ruleId 的最新一条日志记录，从中获取 runId
            val latestLog = ruleExecutionLogRepository.findTopByRuleIdOrderByTimestampDesc(ruleId)
            val elapsedTime = System.currentTimeMillis() - startTime

            if (latestLog != null) {
                log.debug("c8d9e0f1 | findLatestRunIdForRule | 完成 | 找到规则最新的runId | ruleId: {} | runId: {} | 耗时: {}ms", 
                    ruleId, latestLog.runId, elapsedTime)
            } else {
                log.debug("c8d9e0f1 | findLatestRunIdForRule | 完成 | 未找到规则的任何日志记录 | ruleId: {} | 耗时: {}ms", 
                    ruleId, elapsedTime)
            }

            return latestLog?.runId
        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error("c8d9e0f1 | findLatestRunIdForRule | 异常 | 查找规则最新的runId失败 | ruleId: {} | 耗时: {}ms | 异常: {}", 
                ruleId, elapsedTime, e.message, e)
            throw e
        }
    }
}
