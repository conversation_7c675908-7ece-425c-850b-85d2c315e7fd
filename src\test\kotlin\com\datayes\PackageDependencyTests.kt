package com.datayes

import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.junit.AnalyzeClasses
import com.tngtech.archunit.junit.ArchTest
import com.tngtech.archunit.lang.ArchRule
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition

@AnalyzeClasses(
    packages = ["com.datayes"],
    importOptions = [ImportOption.DoNotIncludeTests::class]
)
class PackageDependencyTests {

    @ArchTest
    val `no package should depend on com datayes spring`: ArchRule =
        ArchRuleDefinition.noClasses()
            .that().resideOutsideOfPackage("com.datayes.spring..")
            .should().dependOnClassesThat()
            .resideInAnyPackage("com.datayes.spring..")
}