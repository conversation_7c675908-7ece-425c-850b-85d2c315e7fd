package com.datayes.spring

import com.datayes.domain.*
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/issues")
@Tag(name = "Issue Management", description = "APIs for managing data quality issues")
class IssueController(
    private val issueRepository: IssueRepository,
    private val techRuleRepository: TechRuleRepository,
    private val issueRunHistoryRepository: IssueRunHistoryRepository,
    private val businessRuleInfoRepository: BusinessRuleInfoRepository,
    private val organizationInfoRepository: OrganizationInfoRepository,
) {

    private val log = LoggerFactory.getLogger(IssueController::class.java)

    /**
     * 标准化列表参数：将空列表、仅包含空白字符串的列表转换为 null
     * 避免生成 IN () 导致的 SQL 语法错误，以及空字符串造成的误匹配
     */
    private fun normalizeListParam(list: List<String>?): List<String>? {
        return list?.filter { it.isNotBlank() }?.takeIf { it.isNotEmpty() }
    }

    @GetMapping
    @Operation(summary = "Get all issues", description = "Returns a list of all data quality issues")
    @ApiResponse(responseCode = "200", description = "Successfully retrieved list of issues")
    fun findAll(): StandardResponse<List<IssueEntity>> {
        log.info("7a3b9c2d | findAll | 开始 | 获取所有问题列表")
        val startTime = System.currentTimeMillis()

        try {
            val issues = issueRepository.findAll().toList()
            val elapsedTime = System.currentTimeMillis() - startTime
            log.info("7a3b9c2d | findAll | 完成 | 获取所有问题列表 | count: {} | 耗时: {}ms", issues.size, elapsedTime)
            return StandardResponse.success("问题列表获取成功", issues)
        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error(
                "7a3b9c2d | findAll | 异常 | 获取所有问题列表失败 | 耗时: {}ms | 异常: {}",
                elapsedTime,
                e.message,
                e
            )
            throw e
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get issue by ID", description = "Returns a single issue by its ID")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Successfully retrieved the issue")])
    fun findById(
        @Parameter(description = "ID of the issue to be retrieved", required = true)
        @PathVariable id: Long,
    ): StandardResponse<IssueEntity> {
        log.info("8b4c0d3e | findById | 开始 | 根据ID获取问题 | id: {}", id)
        val startTime = System.currentTimeMillis()

        try {
            val result = issueRepository.findById(id)
            val elapsedTime = System.currentTimeMillis() - startTime

            return if (result.isPresent) {
                log.info("8b4c0d3e | findById | 完成 | 根据ID获取问题成功 | id: {} | 耗时: {}ms", id, elapsedTime)
                StandardResponse.success("问题获取成功", result.get())
            } else {
                log.warn("8b4c0d3e | findById | 完成 | 未找到指定ID的问题 | id: {} | 耗时: {}ms", id, elapsedTime)
                StandardResponse.failure("未找到ID为${id}的问题")
            }
        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error(
                "8b4c0d3e | findById | 异常 | 根据ID获取问题失败 | id: {} | 耗时: {}ms | 异常: {}",
                id, elapsedTime, e.message, e
            )
            throw e
        }
    }

    @GetMapping("/search")
    @Operation(
        summary = "Search issues with filters",
        description = "Returns a filtered list of data quality issues based on search criteria"
    )
    @ApiResponse(responseCode = "200", description = "Successfully retrieved filtered list of issues")
    fun searchIssues(
        @Parameter(description = "校验任务ID") @RequestParam(required = false) taskId: Long?,
        @Parameter(description = "校验任务名称（模糊搜索）") @RequestParam(required = false) taskName: String?,
        @Parameter(description = "所属系统（多选）") @RequestParam(required = false) systemList: List<String>?,
        @Parameter(description = "技术规则名称（模糊搜索）") @RequestParam(required = false) ruleName: String?,
        @Parameter(description = "业务规则名称（模糊搜索）") @RequestParam(required = false) businessRuleName: String?,
        @Parameter(description = "组织名称（模糊搜索）") @RequestParam(required = false) orgName: String?,
        @Parameter(description = "负责人（多选）") @RequestParam(required = false) assignedTo: List<String>?,
        @Parameter(description = "是否已修复（true: 已修复, false: 未修复）") @RequestParam(required = false) isFixed: Boolean?,
        @Parameter(description = "排序字段（目前仅支持：remainingCount）") @RequestParam(required = false) sortField: String?,
        @Parameter(description = "排序方向（ASC: 升序, DESC: 降序）") @RequestParam(required = false, defaultValue = "DESC") sortOrder: String,
        @Parameter(description = "页码，从1开始") @RequestParam(defaultValue = "1") page: Int,
        @Parameter(description = "每页记录数") @RequestParam(defaultValue = "20") size: Int,
    ): StandardResponse<PagedResponse<IssueWithRule>> {
        log.info(
            "9c5d1e2f | searchIssues | 开始 | 搜索问题 | taskId: {} | taskName: {} | systemList: {} | ruleName: {} | businessRuleName: {} | orgName: {} | assignedTo: {} | isFixed: {} | sortField: {} | sortOrder: {} | page: {} | size: {}",
            taskId, taskName, systemList, ruleName, businessRuleName, orgName, assignedTo, isFixed, sortField, sortOrder, page, size
        )
        val startTime = System.currentTimeMillis()

        try {
            // 验证排序参数
            if (sortField != null && sortField != "remainingCount") {
                log.warn("9c5d1e2f | searchIssues | 参数验证失败 | 不支持的排序字段: {}", sortField)
                return StandardResponse.failure("排序字段仅支持：remainingCount")
            }
            if (sortOrder.uppercase() !in listOf("ASC", "DESC")) {
                log.warn("9c5d1e2f | searchIssues | 参数验证失败 | 不支持的排序方向: {}", sortOrder)
                return StandardResponse.failure("排序方向仅支持：ASC, DESC")
            }
            
            val offset = (page - 1) * size
            log.debug("9c5d1e2f | searchIssues | 计算分页参数 | offset: {} | size: {}", offset, size)

            // 标准化列表参数，避免空列表导致的 IN () 语法错误
            val normalizedSystemList = normalizeListParam(systemList)
            val normalizedAssignedTo = normalizeListParam(assignedTo)
            log.info("9c5d1e2f | searchIssues | 参数标准化 | systemList: {} -> {} | assignedTo: {} -> {}", systemList, normalizedSystemList, assignedTo, normalizedAssignedTo)

            // 查询问题列表（将按 ruleId 的最新记录去重下推至 SQL）
            val issues =
                if (taskId != null) {
                    issueRepository.searchIssuesGroupedByRuleIdWithTaskId(
                        taskId,
                        taskName,
                        normalizedSystemList,
                        ruleName,
                        businessRuleName,
                        orgName,
                        normalizedAssignedTo,
                        isFixed,
                        sortField,
                        sortOrder.uppercase(),
                        size,
                        offset
                    ).toList()
                } else {
                    issueRepository.searchIssuesGroupedByRuleId(
                        taskName,
                        normalizedSystemList,
                        ruleName,
                        businessRuleName,
                        orgName,
                        normalizedAssignedTo,
                        isFixed,
                        sortField,
                        sortOrder.uppercase(),
                        size,
                        offset
                    ).toList()
                }
            log.debug("9c5d1e2f | searchIssues | 查询问题列表完成 | count: {}", issues.size)

            // SQL 已完成去重
            val filteredIssues = issues

            // 合并问题和规则信息
            log.debug("9c5d1e2f | searchIssues | 开始合并问题和规则信息")
            val content = filteredIssues.mapNotNull {
                val ruleId = it.ruleId
                val issueId = it.id ?: error("ID should not be null")
                val ruleEntityOptional = techRuleRepository.findById(ruleId)

                if (ruleEntityOptional.isPresent) {
                    val ruleEntity = ruleEntityOptional.get()
                    val first = issueRunHistoryRepository.findTopByIssueIdOrderByStartTimeAsc(it.id)
                    val last = issueRunHistoryRepository.findTopByIssueIdOrderByStartTimeDesc(it.id)
                    
                    // 获取业务规则和组织信息
                    val businessRule = ruleEntity.businessRuleId?.let { businessRuleId ->
                        businessRuleInfoRepository.findById(businessRuleId).orElse(null)
                    }
                    val organization = businessRule?.orgCode?.let { orgCode ->
                        organizationInfoRepository.findById(orgCode).orElse(null)
                    }
                    
                    mergeIssueAndRule(it, ruleEntity, first, last, businessRule, organization)
                } else {
                    log.warn("9c5d1e2f | searchIssues | 未找到对应的规则 | issueId: {} | ruleId: {}", issueId, ruleId)
                    null
                }
            }
            log.debug("9c5d1e2f | searchIssues | 合并问题和规则信息完成 | count: {}", content.size)

            // 查询总数并计算分页信息
            val total = if (taskId != null) {
                issueRepository.searchIssuesCountGroupByRuleWithTaskId(
                    taskId,
                    taskName,
                    normalizedSystemList,
                    ruleName,
                    businessRuleName,
                    orgName,
                    normalizedAssignedTo,
                    isFixed
                )
            } else {
                issueRepository.searchIssuesCountGroupByRule(
                    taskName,
                    normalizedSystemList,
                    ruleName,
                    businessRuleName,
                    orgName,
                    normalizedAssignedTo,
                    isFixed
                )
            }
            val totalPages = if (size > 0) ((total + size - 1) / size).toInt() else 0
            log.debug("9c5d1e2f | searchIssues | 查询总数完成 | total: {} | totalPages: {}", total, totalPages)

            val result = PagedResponse(content, page, size, total, totalPages)

            val elapsedTime = System.currentTimeMillis() - startTime
            log.info(
                "9c5d1e2f | searchIssues | 完成 | 搜索问题成功 | 结果数量: {} | 总数: {} | 耗时: {}ms",
                content.size, total, elapsedTime
            )

            return StandardResponse.success("问题列表获取成功", result)
        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error(
                "9c5d1e2f | searchIssues | 异常 | 搜索问题失败 | taskId: {} | taskName: {} | isFixed: {} | 耗时: {}ms | 异常: {}",
                taskId, taskName, isFixed, elapsedTime, e.message, e
            )
            throw e
        }
    }

    @GetMapping("/run-history")
    @Operation(
        summary = "Get run history by task IDs or rule IDs",
        description = "Returns the most recent run history record for each taskId and ruleId combination, including tech rule details. Client can query by taskId or ruleId, but not both."
    )
    @ApiResponse(responseCode = "200", description = "Successfully retrieved run history records")
    fun getRunHistory(
        @Parameter(description = "List of task IDs")
        @RequestParam(required = false) taskIds: List<Long>?,
        @Parameter(description = "List of rule IDs")
        @RequestParam(required = false) ruleIds: List<Long>?,
    ): StandardResponse<Map<Long, List<RunHistoryWithRule>>> {
        log.info("a6e7f8g9 | getRunHistory | 开始 | 获取运行历史 | taskIds: {} | ruleIds: {}", taskIds, ruleIds)
        val startTime = System.currentTimeMillis()

        try {
            // Validate that either taskIds or ruleIds is provided, but not both
            if ((taskIds == null || taskIds.isEmpty()) && (ruleIds == null || ruleIds.isEmpty())) {
                log.warn("a6e7f8g9 | getRunHistory | 参数验证失败 | 必须提供taskIds或ruleIds")
                return StandardResponse.failure("Either taskIds or ruleIds must be provided")
            }
            if (taskIds != null && taskIds.isNotEmpty() && ruleIds != null && ruleIds.isNotEmpty()) {
                log.warn("a6e7f8g9 | getRunHistory | 参数验证失败 | 不能同时提供taskIds和ruleIds")
                return StandardResponse.failure("Cannot provide both taskIds and ruleIds, please use only one parameter")
            }

            val result = mutableMapOf<Long, List<RunHistoryWithRule>>()

            if (taskIds != null && taskIds.isNotEmpty()) {
                log.debug("a6e7f8g9 | getRunHistory | 开始处理taskIds | count: {}", taskIds.size)
                // Process by taskIds
                for (taskId in taskIds) {
                    log.debug("a6e7f8g9 | getRunHistory | 处理taskId | taskId: {}", taskId)
                    val runHistories = issueRunHistoryRepository.findAllByTaskId(taskId)
                    log.debug(
                        "a6e7f8g9 | getRunHistory | 查询到运行历史 | taskId: {} | count: {}",
                        taskId,
                        runHistories.size
                    )

                    // Group by ruleId, then for each group, sort by startTime desc and take the first one
                    val latestRunHistories = runHistories
                        .groupBy { it.ruleId }
                        .mapValues { (_, histories) ->
                            histories.maxByOrNull { it.startTime }
                        }
                        .mapNotNull { it.value }
                    log.debug(
                        "a6e7f8g9 | getRunHistory | 获取最新运行历史 | taskId: {} | count: {}",
                        taskId,
                        latestRunHistories.size
                    )

                    // For each run history, fetch the corresponding tech rule and merge them
                    val runHistoriesWithRules = latestRunHistories.mapNotNull { runHistory ->
                        val techRuleOptional = techRuleRepository.findById(runHistory.ruleId)
                        if (techRuleOptional.isPresent) {
                            val techRule = techRuleOptional.get()
                            mergeRunHistoryAndRule(runHistory, techRule)
                        } else {
                            log.warn(
                                "a6e7f8g9 | getRunHistory | 未找到对应的规则 | taskId: {} | ruleId: {}",
                                taskId,
                                runHistory.ruleId
                            )
                            null
                        }
                    }
                    log.debug(
                        "a6e7f8g9 | getRunHistory | 合并规则信息完成 | taskId: {} | count: {}",
                        taskId,
                        runHistoriesWithRules.size
                    )

                    result[taskId] = runHistoriesWithRules
                }
            } else if (ruleIds != null && ruleIds.isNotEmpty()) {
                log.debug("a6e7f8g9 | getRunHistory | 开始处理ruleIds | count: {}", ruleIds.size)
                // Process by ruleIds
                for (ruleId in ruleIds) {
                    log.debug("a6e7f8g9 | getRunHistory | 处理ruleId | ruleId: {}", ruleId)
                    val runHistories = issueRunHistoryRepository.findAllByRuleId(ruleId)
                    log.debug(
                        "a6e7f8g9 | getRunHistory | 查询到运行历史 | ruleId: {} | count: {}",
                        ruleId,
                        runHistories.size
                    )

                    // Group by taskId, then for each group, sort by startTime desc and take the first one
                    val latestRunHistories = runHistories
                        .groupBy { it.taskId }
                        .mapValues { (_, histories) ->
                            histories.maxByOrNull { it.startTime }
                        }
                        .mapNotNull { it.value }
                    log.debug(
                        "a6e7f8g9 | getRunHistory | 获取最新运行历史 | ruleId: {} | count: {}",
                        ruleId,
                        latestRunHistories.size
                    )

                    // For each run history, fetch the corresponding tech rule and merge them
                    val runHistoriesWithRules = latestRunHistories.mapNotNull { runHistory ->
                        val techRuleOptional = techRuleRepository.findById(runHistory.ruleId)
                        if (techRuleOptional.isPresent) {
                            val techRule = techRuleOptional.get()
                            mergeRunHistoryAndRule(runHistory, techRule)
                        } else {
                            log.warn(
                                "a6e7f8g9 | getRunHistory | 未找到对应的规则 | taskId: {} | ruleId: {}",
                                runHistory.taskId,
                                ruleId
                            )
                            null
                        }
                    }
                    log.debug(
                        "a6e7f8g9 | getRunHistory | 合并规则信息完成 | ruleId: {} | count: {}",
                        ruleId,
                        runHistoriesWithRules.size
                    )

                    result[ruleId] = runHistoriesWithRules
                }
            }

            // sort result by startTime asc
            val sortedResult = result.mapValues { (_, runHistories) ->
                runHistories.sortedBy { it.startTime }
            }

            val totalRecords = sortedResult.values.sumOf { it.size }
            val elapsedTime = System.currentTimeMillis() - startTime
            log.info(
                "a6e7f8g9 | getRunHistory | 完成 | 获取运行历史成功 | 键数量: {} | 总记录数: {} | 耗时: {}ms",
                sortedResult.size, totalRecords, elapsedTime
            )

            return StandardResponse.success("Run history records retrieved successfully", sortedResult)
        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error(
                "a6e7f8g9 | getRunHistory | 异常 | 获取运行历史失败 | taskIds: {} | ruleIds: {} | 耗时: {}ms | 异常: {}",
                taskIds, ruleIds, elapsedTime, e.message, e
            )
            throw e
        }
    }

    @GetMapping("/run-history/by-run")
    @Operation(
        summary = "Get run history by task ID and run ID",
        description = "Returns run history records for the specified task ID and run ID, including tech rule details"
    )
    @ApiResponse(responseCode = "200", description = "Successfully retrieved run history records")
    fun getRunHistoryByTaskIdAndRunId(
        @Parameter(description = "Task ID", required = true)
        @RequestParam taskId: Long,
        @Parameter(description = "Run ID", required = true)
        @RequestParam runId: String,
    ): StandardResponse<List<RunHistoryWithRule>> {
        log.info(
            "b7h8i9j0 | getRunHistoryByTaskIdAndRunId | 开始 | 根据任务ID和运行ID获取运行历史 | taskId: {} | runId: {}",
            taskId,
            runId
        )
        val startTime = System.currentTimeMillis()

        try {
            val runHistories = issueRunHistoryRepository.findByTaskIdAndRunId(taskId, runId)
            log.debug(
                "b7h8i9j0 | getRunHistoryByTaskIdAndRunId | 查询到运行历史 | taskId: {} | runId: {} | count: {}",
                taskId, runId, runHistories.size
            )

            // For each run history, fetch the corresponding tech rule and merge them
            log.debug("b7h8i9j0 | getRunHistoryByTaskIdAndRunId | 开始合并规则信息")
            val runHistoriesWithRules = runHistories.mapNotNull { runHistory ->
                val techRuleOptional = techRuleRepository.findById(runHistory.ruleId)
                if (techRuleOptional.isPresent) {
                    val techRule = techRuleOptional.get()
                    mergeRunHistoryAndRule(runHistory, techRule)
                } else {
                    log.warn(
                        "b7h8i9j0 | getRunHistoryByTaskIdAndRunId | 未找到对应的规则 | taskId: {} | runId: {} | ruleId: {}",
                        taskId, runId, runHistory.ruleId
                    )
                    null
                }
            }
            log.debug(
                "b7h8i9j0 | getRunHistoryByTaskIdAndRunId | 合并规则信息完成 | count: {}",
                runHistoriesWithRules.size
            )

            val elapsedTime = System.currentTimeMillis() - startTime

            return if (runHistoriesWithRules.isNotEmpty()) {
                log.info(
                    "b7h8i9j0 | getRunHistoryByTaskIdAndRunId | 完成 | 获取运行历史成功 | taskId: {} | runId: {} | count: {} | 耗时: {}ms",
                    taskId, runId, runHistoriesWithRules.size, elapsedTime
                )
                StandardResponse.success("Run history records retrieved successfully", runHistoriesWithRules)
            } else {
                log.warn(
                    "b7h8i9j0 | getRunHistoryByTaskIdAndRunId | 完成 | 未找到运行历史记录 | taskId: {} | runId: {} | 耗时: {}ms",
                    taskId, runId, elapsedTime
                )
                // StandardResponse.failure("No run history records found for taskId $taskId and runId $runId")
                StandardResponse.failure("该执行记录已被删除")
            }
        } catch (e: Exception) {
            val elapsedTime = System.currentTimeMillis() - startTime
            log.error(
                "b7h8i9j0 | getRunHistoryByTaskIdAndRunId | 异常 | 获取运行历史失败 | taskId: {} | runId: {} | 耗时: {}ms | 异常: {}",
                taskId, runId, elapsedTime, e.message, e
            )
            throw e
        }
    }

}
