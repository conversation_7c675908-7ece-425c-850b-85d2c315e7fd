package com.datayes.spring

import com.datayes.domain.IssueRunHistoryEntity
import org.springframework.data.repository.CrudRepository
import org.springframework.stereotype.Repository

@Repository
interface IssueRunHistoryRepository : CrudRepository<IssueRunHistoryEntity, Long> {

    // find by rule id and order by startTime desc and limit 1
    fun findTopByIssueIdOrderByStartTimeDesc(issueId: Long): IssueRunHistoryEntity?

    // find by rule id and order by startTime asc and limit 1
    fun findTopByIssueIdOrderByStartTimeAsc(issueId: Long): IssueRunHistoryEntity?

    // find all run histories by taskId
    fun findAllByTaskId(taskId: Long): List<IssueRunHistoryEntity>

    // find all run histories by ruleId
    fun findAllByRuleId(ruleId: Long): List<IssueRunHistoryEntity>

    // find run histories by taskId and runId
    fun findByTaskIdAndRunId(taskId: Long, runId: String): List<IssueRunHistoryEntity>
}
