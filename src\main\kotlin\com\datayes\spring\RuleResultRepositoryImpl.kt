package com.datayes.spring

import com.datayes.domain.RuleResultEntity
import com.datayes.domain.JsonFilter
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.stereotype.Repository
import java.sql.ResultSet

@Repository
class RuleResultRepositoryImpl {

    @Autowired
    private lateinit var jdbcTemplate: JdbcTemplate
    
    private val log = org.slf4j.LoggerFactory.getLogger(RuleResultRepositoryImpl::class.java)

    private val ruleResultRowMapper = RowMapper<RuleResultEntity> { rs: ResultSet, _: Int ->
        RuleResultEntity(
            id = rs.getLong("id"),
            ruleId = rs.getLong("RULE_ID"),
            dbType = rs.getString("DB_TYPE"),
            originalSql = rs.getString("ORIGINAL_SQL"),
            runId = rs.getString("RUN_ID"),
            executedAt = rs.getTimestamp("EXECUTED_AT").toLocalDateTime(),
            rowData = rs.getString("ROW_DATA"),
            rowDataMd5 = rs.getString("BUSINESS_KEY_DATA_MD5"),
            rowIndex = rs.getInt("ROW_INDEX"),
            executionTimeMs = rs.getLong("EXECUTION_TIME_MS"),
            isFixed = rs.getInt("IS_FIXED"),
            softwareDevReqNumberList = rs.getString("SOFTWARE_DEV_REQ_NUMBER_LIST"),
            assignmentStatus = rs.getString("ASSIGNMENT_STATUS") ?: "未分配",
            issueCauseType = rs.getString("ISSUE_CAUSE_TYPE"),
            issueCauseDetail = rs.getString("ISSUE_CAUSE_DETAIL"),
            createdBy = rs.getString("CREATED_BY"),
            createdAt = rs.getTimestamp("CREATED_AT").toLocalDateTime(),
            updatedBy = rs.getString("UPDATED_BY"),
            updatedAt = rs.getTimestamp("UPDATED_AT")?.toLocalDateTime()
        )
    }

    fun findAllByRuleIdOrderByUpdatedAtDescWithPagination(
        ruleId: Long,
        limit: Int,
        offset: Int
    ): List<RuleResultEntity> {
        val sql = "SELECT * FROM rule_execution_results WHERE rule_id = ? ORDER BY updated_at DESC LIMIT ? OFFSET ?"
        return jdbcTemplate.query(sql, ruleResultRowMapper, ruleId, limit, offset)
    }

    fun findByRuleIdAndRunIdOrderByUpdatedAtDescWithPagination(
        ruleId: Long,
        runId: String,
        limit: Int,
        offset: Int
    ): List<RuleResultEntity> {
        val sql =
            "SELECT * FROM rule_execution_results WHERE rule_id = ? AND run_id = ? ORDER BY updated_at DESC LIMIT ? OFFSET ?"
        return jdbcTemplate.query(sql, ruleResultRowMapper, ruleId, runId, limit, offset)
    }

    // Non-paginated methods for Excel export
    fun findAllByRuleIdOrderByUpdatedAtDesc(ruleId: Long): List<RuleResultEntity> {
        val sql = "SELECT * FROM rule_execution_results WHERE rule_id = ? ORDER BY updated_at DESC"
        return jdbcTemplate.query(sql, ruleResultRowMapper, ruleId)
    }

    fun findByRuleIdAndRunIdOrderByUpdatedAtDesc(ruleId: Long, runId: String): List<RuleResultEntity> {
        val sql = "SELECT * FROM rule_execution_results WHERE rule_id = ? AND run_id = ? ORDER BY updated_at DESC"
        return jdbcTemplate.query(sql, ruleResultRowMapper, ruleId, runId)
    }

    // Batch processing methods for large datasets
    fun findAllByRuleIdOrderByUpdatedAtDescBatch(ruleId: Long, batchSize: Int, offset: Int): List<RuleResultEntity> {
        val sql = "SELECT * FROM rule_execution_results WHERE rule_id = ? ORDER BY updated_at DESC LIMIT ? OFFSET ?"
        return jdbcTemplate.query(sql, ruleResultRowMapper, ruleId, batchSize, offset)
    }

    fun findByRuleIdAndRunIdOrderByUpdatedAtDescBatch(
        ruleId: Long,
        runId: String,
        batchSize: Int,
        offset: Int
    ): List<RuleResultEntity> {
        val sql =
            "SELECT * FROM rule_execution_results WHERE rule_id = ? AND run_id = ? ORDER BY updated_at DESC LIMIT ? OFFSET ?"
        return jdbcTemplate.query(sql, ruleResultRowMapper, ruleId, runId, batchSize, offset)
    }

    // Cursor-based batch processing methods for better performance with large datasets
    fun findAllByRuleIdOrderByUpdatedAtDescCursor(
        ruleId: Long, 
        batchSize: Int, 
        lastUpdatedAt: java.time.LocalDateTime?,
        lastId: Long?
    ): List<RuleResultEntity> {
        val sql = if (lastUpdatedAt == null) {
            "SELECT * FROM rule_execution_results WHERE rule_id = ? ORDER BY updated_at DESC, id DESC LIMIT ?"
        } else {
            "SELECT * FROM rule_execution_results WHERE rule_id = ? AND (updated_at < ? OR (updated_at = ? AND id < ?)) ORDER BY updated_at DESC, id DESC LIMIT ?"
        }
        
        val startTime = System.currentTimeMillis()
        log.info("SQL_QUERY | findAllByRuleIdOrderByUpdatedAtDescCursor | 开始 | ruleId: {} | batchSize: {} | lastUpdatedAt: {} | lastId: {} | SQL: {}", 
            ruleId, batchSize, lastUpdatedAt, lastId, sql)
        
        val result = if (lastUpdatedAt == null) {
            jdbcTemplate.query(sql, ruleResultRowMapper, ruleId, batchSize)
        } else {
            jdbcTemplate.query(sql, ruleResultRowMapper, ruleId, 
                java.sql.Timestamp.valueOf(lastUpdatedAt), 
                java.sql.Timestamp.valueOf(lastUpdatedAt), 
                lastId, batchSize)
        }
        
        val elapsedTime = System.currentTimeMillis() - startTime
        log.info("SQL_QUERY | findAllByRuleIdOrderByUpdatedAtDescCursor | 完成 | ruleId: {} | 返回记录数: {} | 耗时: {}ms", 
            ruleId, result.size, elapsedTime)
        
        return result
    }

    fun findByRuleIdAndRunIdOrderByUpdatedAtDescCursor(
        ruleId: Long,
        runId: String,
        batchSize: Int,
        lastUpdatedAt: java.time.LocalDateTime?,
        lastId: Long?
    ): List<RuleResultEntity> {
        val sql = if (lastUpdatedAt == null) {
            "SELECT * FROM rule_execution_results WHERE rule_id = ? AND run_id = ? ORDER BY updated_at DESC, id DESC LIMIT ?"
        } else {
            "SELECT * FROM rule_execution_results WHERE rule_id = ? AND run_id = ? AND (updated_at < ? OR (updated_at = ? AND id < ?)) ORDER BY updated_at DESC, id DESC LIMIT ?"
        }
        
        val startTime = System.currentTimeMillis()
        log.info("SQL_QUERY | findByRuleIdAndRunIdOrderByUpdatedAtDescCursor | 开始 | ruleId: {} | runId: {} | batchSize: {} | lastUpdatedAt: {} | lastId: {} | SQL: {}", 
            ruleId, runId, batchSize, lastUpdatedAt, lastId, sql)
        
        val result = if (lastUpdatedAt == null) {
            jdbcTemplate.query(sql, ruleResultRowMapper, ruleId, runId, batchSize)
        } else {
            jdbcTemplate.query(sql, ruleResultRowMapper, ruleId, runId, 
                java.sql.Timestamp.valueOf(lastUpdatedAt), 
                java.sql.Timestamp.valueOf(lastUpdatedAt), 
                lastId, batchSize)
        }
        
        val elapsedTime = System.currentTimeMillis() - startTime
        log.info("SQL_QUERY | findByRuleIdAndRunIdOrderByUpdatedAtDescCursor | 完成 | ruleId: {} | runId: {} | 返回记录数: {} | 耗时: {}ms", 
            ruleId, runId, result.size, elapsedTime)
        
        return result
    }

    // Get total count for batch processing
    fun countByRuleIdForBatch(ruleId: Long): Long {
        val sql = "SELECT COUNT(*) FROM rule_execution_results WHERE rule_id = ?"
        return jdbcTemplate.queryForObject(sql, Long::class.java, ruleId) ?: 0L
    }

    fun countByRuleIdAndRunIdForBatch(ruleId: Long, runId: String): Long {
        val sql = "SELECT COUNT(*) FROM rule_execution_results WHERE rule_id = ? AND run_id = ?"
        return jdbcTemplate.queryForObject(sql, Long::class.java, ruleId, runId) ?: 0L
    }

    // JSON filtering methods
    fun findAllByRuleIdWithJsonFiltersOrderByUpdatedAtDescWithPagination(
        ruleId: Long,
        jsonFilters: List<JsonFilter>,
        limit: Int,
        offset: Int
    ): List<RuleResultEntity> {
        val conditions = buildJsonConditions(jsonFilters)
        val sql =
            "SELECT * FROM rule_execution_results WHERE rule_id = ? AND $conditions ORDER BY updated_at DESC LIMIT ? OFFSET ?"
        val params = listOf(ruleId) + jsonFilters.map { it.value.trim() } + listOf(limit, offset)
        return jdbcTemplate.query(sql, ruleResultRowMapper, *params.toTypedArray())
    }

    fun findByRuleIdAndRunIdWithJsonFiltersOrderByUpdatedAtDescWithPagination(
        ruleId: Long,
        runId: String,
        jsonFilters: List<JsonFilter>,
        limit: Int,
        offset: Int
    ): List<RuleResultEntity> {
        val conditions = buildJsonConditions(jsonFilters)
        val sql =
            "SELECT * FROM rule_execution_results WHERE rule_id = ? AND run_id = ? AND $conditions ORDER BY updated_at DESC LIMIT ? OFFSET ?"
        val params = listOf(ruleId, runId) + jsonFilters.map { it.value.trim() } + listOf(limit, offset)
        return jdbcTemplate.query(sql, ruleResultRowMapper, *params.toTypedArray())
    }

    fun countByRuleIdWithJsonFilters(ruleId: Long, jsonFilters: List<JsonFilter>): Long {
        val conditions = buildJsonConditions(jsonFilters)
        val sql = "SELECT COUNT(*) FROM rule_execution_results WHERE rule_id = ? AND $conditions"
        val params = listOf(ruleId) + jsonFilters.map { it.value.trim() }
        return jdbcTemplate.queryForObject(sql, Long::class.java, *params.toTypedArray()) ?: 0L
    }

    fun countByRuleIdAndRunIdWithJsonFilters(ruleId: Long, runId: String, jsonFilters: List<JsonFilter>): Long {
        val conditions = buildJsonConditions(jsonFilters)
        val sql = "SELECT COUNT(*) FROM rule_execution_results WHERE rule_id = ? AND run_id = ? AND $conditions"
        val params = listOf(ruleId, runId) + jsonFilters.map { it.value.trim() }
        return jdbcTemplate.queryForObject(sql, Long::class.java, *params.toTypedArray()) ?: 0L
    }

    private fun buildJsonConditions(jsonFilters: List<JsonFilter>): String {
        return jsonFilters.joinToString(" AND ") { filter ->
            // 对filter.key进行白名单校验，防止SQL注入
            // 支持英文字母、数字、下划线、中文字符，以及由点号分隔的多级字段
            if (!filter.key.matches(Regex("^[a-zA-Z0-9_\\u4e00-\\u9fff]+(\\.[a-zA-Z0-9_\\u4e00-\\u9fff]+)*$"))) {
                throw IllegalArgumentException("不支持的过滤字段: ${filter.key}")
            }
            // 使用双引号包围key以处理包含点号的key名
            "TRIM(JSON_UNQUOTE(JSON_EXTRACT(ROW_DATA, '$.\"${filter.key}\"'))) = ?"
        }
    }

    // Enhanced search methods with additional filtering
    fun findWithEnhancedFiltersOrderByUpdatedAtDescWithPagination(
        ruleId: Long,
        runId: String?,
        jsonFilters: List<JsonFilter>,
        isFixed: Int?,
        assignmentStatus: String?,
        softwareDevReqNumber: String?,
        issueCauseType: String?,
        issueCauseDetail: String?,
        limit: Int,
        offset: Int
    ): List<RuleResultEntity> {
        val conditions = mutableListOf<String>()
        val params = mutableListOf<Any>()
        
        // Base conditions
        conditions.add("rule_id = ?")
        params.add(ruleId)
        
        if (!runId.isNullOrBlank()) {
            conditions.add("run_id = ?")
            params.add(runId)
        }
        
        // JSON filters
        if (jsonFilters.isNotEmpty()) {
            conditions.add(buildJsonConditions(jsonFilters))
            params.addAll(jsonFilters.map { it.value.trim() })
        }
        
        // New filter conditions
        if (isFixed != null) {
            conditions.add("IS_FIXED = ?")
            params.add(isFixed)
        }
        
        if (!assignmentStatus.isNullOrBlank()) {
            conditions.add("ASSIGNMENT_STATUS = ?")
            params.add(assignmentStatus.trim())
        }
        
        if (!softwareDevReqNumber.isNullOrBlank()) {
            conditions.add("JSON_CONTAINS(SOFTWARE_DEV_REQ_NUMBER_LIST, JSON_QUOTE(?)) OR SOFTWARE_DEV_REQ_NUMBER_LIST LIKE ?")
            params.add(softwareDevReqNumber.trim())
            params.add("%${softwareDevReqNumber.trim()}%")
        }
        
        if (!issueCauseType.isNullOrBlank()) {
            conditions.add("ISSUE_CAUSE_TYPE = ?")
            params.add(issueCauseType.trim())
        }
        
        if (!issueCauseDetail.isNullOrBlank()) {
            conditions.add("ISSUE_CAUSE_DETAIL LIKE ?")
            params.add("%${issueCauseDetail.trim()}%")
        }
        
        val whereClause = conditions.joinToString(" AND ")
        val sql = "SELECT * FROM rule_execution_results WHERE $whereClause ORDER BY updated_at DESC LIMIT ? OFFSET ?"
        params.addAll(listOf(limit, offset))
        
        return jdbcTemplate.query(sql, ruleResultRowMapper, *params.toTypedArray())
    }

    fun countWithEnhancedFilters(
        ruleId: Long,
        runId: String?,
        jsonFilters: List<JsonFilter>,
        isFixed: Int?,
        assignmentStatus: String?,
        softwareDevReqNumber: String?,
        issueCauseType: String?,
        issueCauseDetail: String?
    ): Long {
        val conditions = mutableListOf<String>()
        val params = mutableListOf<Any>()
        
        // Base conditions
        conditions.add("rule_id = ?")
        params.add(ruleId)
        
        if (!runId.isNullOrBlank()) {
            conditions.add("run_id = ?")
            params.add(runId)
        }
        
        // JSON filters
        if (jsonFilters.isNotEmpty()) {
            conditions.add(buildJsonConditions(jsonFilters))
            params.addAll(jsonFilters.map { it.value.trim() })
        }
        
        // New filter conditions
        if (isFixed != null) {
            conditions.add("IS_FIXED = ?")
            params.add(isFixed)
        }
        
        if (!assignmentStatus.isNullOrBlank()) {
            conditions.add("ASSIGNMENT_STATUS = ?")
            params.add(assignmentStatus.trim())
        }
        
        if (!softwareDevReqNumber.isNullOrBlank()) {
            conditions.add("JSON_CONTAINS(SOFTWARE_DEV_REQ_NUMBER_LIST, JSON_QUOTE(?)) OR SOFTWARE_DEV_REQ_NUMBER_LIST LIKE ?")
            params.add(softwareDevReqNumber.trim())
            params.add("%${softwareDevReqNumber.trim()}%")
        }
        
        if (!issueCauseType.isNullOrBlank()) {
            conditions.add("ISSUE_CAUSE_TYPE = ?")
            params.add(issueCauseType.trim())
        }
        
        if (!issueCauseDetail.isNullOrBlank()) {
            conditions.add("ISSUE_CAUSE_DETAIL LIKE ?")
            params.add("%${issueCauseDetail.trim()}%")
        }
        
        val whereClause = conditions.joinToString(" AND ")
        val sql = "SELECT COUNT(*) FROM rule_execution_results WHERE $whereClause"
        
        return jdbcTemplate.queryForObject(sql, Long::class.java, *params.toTypedArray()) ?: 0L
    }
}