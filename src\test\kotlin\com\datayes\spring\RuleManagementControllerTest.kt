package com.datayes.spring

import com.datayes.domain.*
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import java.time.LocalDateTime

@WebMvcTest(RuleManagementController::class)
class RuleManagementControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockkBean
    private lateinit var issueRepository: IssueRepository

    @MockkBean
    private lateinit var issueRunHistoryRepository: IssueRunHistoryRepository

    @MockkBean
    private lateinit var ruleExecutionLogRepository: RuleExecutionLogRepository

    @MockkBean
    private lateinit var ruleResultRepository: RuleResultRepository

    @Test
    fun `should delete all data related to a rule ID`() {
        // Given
        val ruleId = 123L
        val now = LocalDateTime.now()

        // Mock rule results
        val ruleResult = RuleResultEntity(
            id = 1L,
            ruleId = ruleId,
            dbType = "MySQL",
            originalSql = "SELECT * FROM test",
            runId = "run-123",
            executedAt = now,
            rowData = "{}",
            rowDataMd5 = "md5-hash",
            rowIndex = 1,
            executionTimeMs = 100L,
            isFixed = 0
        )
        every { ruleResultRepository.findAllByRuleId(ruleId) } returns listOf(ruleResult)
        every { ruleResultRepository.delete(any()) } returns Unit

        // Mock execution logs
        val executionLog = RuleExecutionLogEntity(
            id = 2L,
            runId = "run-123",
            ruleId = ruleId,
            taskId = 456L,
            timestamp = now,
            level = "INFO",
            message = "Test message"
        )
        every { ruleExecutionLogRepository.findAllByRuleId(ruleId) } returns listOf(executionLog)
        every { ruleExecutionLogRepository.delete(any()) } returns Unit

        // Mock run histories
        val runHistory = IssueRunHistoryEntity(
            id = 3L,
            issueId = 789L,
            taskId = 456L,
            ruleId = ruleId,
            runId = "run-123",
            startTime = now,
            status = RunStatus.COMPLETED
        )
        every { issueRunHistoryRepository.findAllByRuleId(ruleId) } returns listOf(runHistory)
        every { issueRunHistoryRepository.delete(any()) } returns Unit

        // Mock issues
        val issue = IssueEntity(
            id = 4L,
            ruleId = ruleId,
            taskId = 456L,
            createBy = "test-user"
        )
        every {
            issueRepository.searchIssues(
                taskId = null,
                taskName = null,
                systemList = null,
                ruleName = null,
                assignedToList = null,
                limit = 1000,
                offset = 0,
                businessRuleName = null,
                orgName = null,
                isFixed = null
            )
        } returns listOf(issue)
        every { issueRepository.delete(any()) } returns Unit

        // When/Then
        mockMvc.post("/api/rule-management/delete-execution-by-rule/$ruleId") {
            contentType = MediaType.APPLICATION_JSON
            accept = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.message") { value("成功删除规则ID $ruleId 的相关数据") }
            jsonPath("$.payload.ruleResults") { value(1) }
            jsonPath("$.payload.executionLogs") { value(1) }
            jsonPath("$.payload.runHistories") { value(1) }
            jsonPath("$.payload.issues") { value(1) }
        }

        // Verify
        verify { ruleResultRepository.delete(ruleResult) }
        verify { ruleExecutionLogRepository.delete(executionLog) }
        verify { issueRunHistoryRepository.delete(runHistory) }
        verify { issueRepository.delete(issue) }
    }
}
