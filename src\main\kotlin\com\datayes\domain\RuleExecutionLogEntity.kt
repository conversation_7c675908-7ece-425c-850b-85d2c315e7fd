package com.datayes.domain

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime

@Table("rule_execution_log")
data class RuleExecutionLogEntity(

    @Id
    val id: Long? = null,

    @Column("RUN_ID")
    val runId: String,

    @Column("RULE_ID")
    val ruleId: Long,

    @Column("TASK_ID")
    val taskId: Long,

    @Column("TIMESTAMP")
    val timestamp: LocalDateTime,

    @Column("LEVEL")
    val level: String, // e.g., INFO, WARN, ERROR

    @Column("MESSAGE")
    val message: String,

    @Column("HOST")
    val host: String? = null,

    @Column("DB_HOST")
    val dbHost: String? = null,

    @Column("DB_NAME")
    val dbName: String? = null,

    @Column("DB_USERNAME")
    val dbUsername: String? = null,

    @Column("SQL_STATUS")
    val sqlStatus: String? = null
)

