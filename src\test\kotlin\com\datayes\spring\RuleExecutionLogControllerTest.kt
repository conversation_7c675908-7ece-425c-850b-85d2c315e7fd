package com.datayes.spring

import com.datayes.domain.RuleExecutionLogEntity
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import org.hamcrest.CoreMatchers.containsString
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import java.time.LocalDateTime

@SpringBootTest
@AutoConfigureMockMvc
class RuleExecutionLogControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockkBean // 使用 MockkBean 模拟 Repository
    private lateinit var ruleExecutionLogRepository: RuleExecutionLogRepository

    @Autowired
    private lateinit var objectMapper: ObjectMapper // 用于处理 JSON

    @Test
    fun `should return logs when rule_id and run_id are provided`() {
        val ruleId = 123L
        val runId = "test-run-abc"
        val expectedLogs = listOf(
            RuleExecutionLogEntity(
                id = 1,
                runId = runId,
                ruleId = ruleId,
                taskId = 456L,
                timestamp = LocalDateTime.now(),
                level = "INFO",
                message = "Log message 1",
                host = "host1"
            ),
            RuleExecutionLogEntity(
                id = 2,
                runId = runId,
                ruleId = ruleId,
                taskId = 456L,
                timestamp = LocalDateTime.now(),
                level = "DEBUG",
                message = "Log message 2",
                host = "host2"
            )
        )

        // 模拟 Repository 方法调用
        every { ruleExecutionLogRepository.findByRuleIdAndRunId(ruleId, runId) } returns expectedLogs

        mockMvc.get("/api/rule-execution-logs") {
            param("rule_id", ruleId.toString())
            param("run_id", runId)
            accept(MediaType.APPLICATION_JSON)
        }.andExpect {
            status { isOk() }
            content { contentType(MediaType.APPLICATION_JSON) }
            jsonPath("$.success") { value(true) }
            jsonPath("$.message") { value("操作成功") } // 默认成功消息
            jsonPath("$.payload.length()") { value(expectedLogs.size) }
            jsonPath("$.payload[0].id") { value(expectedLogs[0].id) }
            jsonPath("$.payload[0].runId") { value(expectedLogs[0].runId) }
            jsonPath("$.payload[0].ruleId") { value(expectedLogs[0].ruleId) }
            jsonPath("$.payload[0].taskId") { value(expectedLogs[0].taskId) }
            jsonPath("$.payload[0].level") { value(expectedLogs[0].level) }
            jsonPath("$.payload[0].message") { value(expectedLogs[0].message) }
            jsonPath("$.payload[0].host") { value(expectedLogs[0].host) }
            // timestamp 比较复杂，可以跳过或使用更精细的匹配
        }
    }

    @Test
    fun `should return logs for latest run when only rule_id is provided`() {
        val ruleId = 123L
        val latestRunId = "latest-run-xyz"
        val latestLogEntry = RuleExecutionLogEntity(
            id = 10,
            runId = latestRunId,
            ruleId = ruleId,
            taskId = 456L,
            timestamp = LocalDateTime.now(),
            level = "INFO",
            message = "Latest log",
            host = "hostA"
        )
        val expectedLogs = listOf(
            RuleExecutionLogEntity(
                id = 11,
                runId = latestRunId,
                ruleId = ruleId,
                taskId = 456L,
                timestamp = LocalDateTime.now(),
                level = "INFO",
                message = "Log for latest run 1",
                host = "hostB"
            ),
            RuleExecutionLogEntity(
                id = 12,
                runId = latestRunId,
                ruleId = ruleId,
                taskId = 456L,
                timestamp = LocalDateTime.now(),
                level = "WARN",
                message = "Log for latest run 2",
                host = "hostC"
            )
        )

        // 模拟 Repository 方法调用：先查找最新日志获取 runId，再根据 ruleId 和最新 runId 查找日志列表
        every { ruleExecutionLogRepository.findTopByRuleIdOrderByTimestampDesc(ruleId) } returns latestLogEntry
        every { ruleExecutionLogRepository.findByRuleIdAndRunId(ruleId, latestRunId) } returns expectedLogs

        mockMvc.get("/api/rule-execution-logs") {
            param("rule_id", ruleId.toString())
            accept(MediaType.APPLICATION_JSON)
        }.andExpect {
            status { isOk() }
            content { contentType(MediaType.APPLICATION_JSON) }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload.length()") { value(expectedLogs.size) }
            jsonPath("$.payload[0].runId") { value(latestRunId) } // 验证返回的日志是最新运行的
            // ... 验证更多字段 ...
        }
    }

    @Test
    fun `should return empty list and message when no logs found for rule_id`() {
        val ruleId = 456L

        // 模拟 Repository 方法调用：找不到最新日志
        every { ruleExecutionLogRepository.findTopByRuleIdOrderByTimestampDesc(ruleId) } returns null

        mockMvc.get("/api/rule-execution-logs") {
            param("rule_id", ruleId.toString())
            accept(MediaType.APPLICATION_JSON)
        }.andExpect {
            status { isOk() }
            content { contentType(MediaType.APPLICATION_JSON) }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload") { isEmpty() } // 验证 payload 是空列表
            jsonPath("$.message") { value("未找到规则ID ${ruleId} 的任何执行日志") } // 验证消息
        }
    }

    @Test
    fun `should success false when rule_id is missing`() {
        mockMvc.get("/api/rule-execution-logs") {
            // 不提供 rule_id 参数
            accept(MediaType.APPLICATION_JSON)
        }.andExpect {
            status { isOk() }
            content { contentType(MediaType.APPLICATION_JSON) }
            jsonPath("$.success") { value(false) }     // 但success字段为false
            jsonPath("$.message") { value(containsString("Required request parameter 'rule_id' for method parameter type long is not present")) }
        }
    }

    @Test
    fun `should return HTTP OK but with success=false when operation fails`() {
        val ruleId = 123L

        // 模拟业务逻辑错误情况 - 例如抛出一个业务异常
        every { ruleExecutionLogRepository.findTopByRuleIdOrderByTimestampDesc(ruleId) } throws
                RuntimeException("模拟的业务逻辑错误")

        mockMvc.get("/api/rule-execution-logs") {
            param("rule_id", ruleId.toString())
            accept(MediaType.APPLICATION_JSON)
        }.andExpect {
            status { isOk() }                          // HTTP状态码是200 OK
            content { contentType(MediaType.APPLICATION_JSON) }
            jsonPath("$.success") { value(false) }     // 但success字段为false
            jsonPath("$.message") { exists() }         // 应该有错误消息
            jsonPath("$.payload") { doesNotExist() }   // payload可能不存在或为null
        }
    }
}