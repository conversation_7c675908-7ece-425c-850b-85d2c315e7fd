-- Performance optimization indexes for /api/issues/search endpoint
-- Date: 2025-08-21
-- Issue: Slow query performance due to missing critical indexes

-- =============================================================================
-- CRITICAL MISSING FOREIGN KEY INDEXES
-- =============================================================================

-- validate_task_rule table foreign key indexes (69K+ rows, currently no indexes)
CREATE INDEX idx_vtr_task_id ON validate_task_rule(TASK_ID);
CREATE INDEX idx_vtr_rule_id ON validate_task_rule(RULE_ID);

-- =============================================================================
-- SEARCH FILTER INDEXES FOR data_quality_issues
-- =============================================================================

-- Frequently filtered columns in search queries
CREATE INDEX idx_dqi_status ON data_quality_issues(STATUS);
CREATE INDEX idx_dqi_assigned_to ON data_quality_issues(ASSIGNED_TO);

-- ORDER BY columns for performance
CREATE INDEX idx_dqi_create_at ON data_quality_issues(CREATE_AT DESC);
CREATE INDEX idx_dqi_update_at ON data_quality_issues(UPDATE_AT DESC);

-- Composite index for the expensive correlated subquery in searchIssuesGroupedByRuleId
-- This optimizes the MAX(UPDATE_AT) subquery grouped by RULE_ID
CREATE INDEX idx_dqi_rule_update ON data_quality_issues(RULE_ID, UPDATE_AT DESC);

-- =============================================================================
-- TASK NAME SEARCH INDEX
-- =============================================================================

-- For LIKE searches on task names (validate_task has 62K+ rows, no indexes)
CREATE INDEX idx_vt_task_name ON validate_task(TASK_NAME);

-- =============================================================================
-- PERFORMANCE IMPACT ESTIMATION
-- =============================================================================
-- Expected improvements:
-- - 5-10x faster query execution for /api/issues/search
-- - Reduced database CPU usage during peak loads  
-- - Better scalability as data volume grows
-- - Elimination of full table scans on large tables

-- =============================================================================
-- DEPLOYMENT NOTES
-- =============================================================================
-- 1. Run during low-traffic periods
-- 2. Monitor query execution plans before/after
-- 3. Consider running indexes concurrently if using MySQL 8.0+
-- 4. Update table statistics after index creation